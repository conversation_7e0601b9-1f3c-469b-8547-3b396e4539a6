<template>
	<view class="print-test">
		<view class="header">
			<text class="title">打印功能测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试图片</text>
			<view class="test-images">
				<view 
					class="test-image-item" 
					v-for="image in testImages" 
					:key="image.id"
					:class="{ selected: selectedImages.includes(image.id) }"
					@click="toggleSelection(image.id)"
				>
					<image class="test-image" :src="image.url" mode="aspectFit" />
					<text class="image-name">{{ image.name }}</text>
				</view>
			</view>
		</view>
		
		<view class="actions">
			<view class="action-btn" @click="testSinglePrint" v-if="selectedImages.length === 1">
				<text class="btn-text">单张打印测试</text>
			</view>
			<view class="action-btn" @click="testBatchPrint" v-if="selectedImages.length > 1">
				<text class="btn-text">批量打印测试 ({{ selectedImages.length }}张)</text>
			</view>
			<view class="action-btn secondary" @click="clearSelection" v-if="selectedImages.length > 0">
				<text class="btn-text">清空选择</text>
			</view>
		</view>
		
		<view class="info">
			<text class="info-text">请选择一张或多张图片进行打印测试</text>
			<text class="info-text">确保您的浏览器允许弹窗，并且已连接打印机</text>
		</view>
	</view>
</template>

<script>
import PrintUtils from '@/utils/printUtils.js'

export default {
	data() {
		return {
			selectedImages: [],
			testImages: [
				{
					id: 1,
					name: '测试图片1.jpg',
					url: 'https://picsum.photos/400/300?random=1',
					factoryName: '测试工厂A',
					orderNumber: 'TEST001',
					uploadDate: new Date().toISOString(),
					fileSizeFormatted: '245.6 KB'
				},
				{
					id: 2,
					name: '测试图片2.jpg',
					url: 'https://picsum.photos/400/300?random=2',
					factoryName: '测试工厂B',
					orderNumber: 'TEST002',
					uploadDate: new Date().toISOString(),
					fileSizeFormatted: '312.8 KB'
				},
				{
					id: 3,
					name: '测试图片3.jpg',
					url: 'https://picsum.photos/400/300?random=3',
					factoryName: '测试工厂C',
					orderNumber: 'TEST003',
					uploadDate: new Date().toISOString(),
					fileSizeFormatted: '189.2 KB'
				},
				{
					id: 4,
					name: '测试图片4.jpg',
					url: 'https://picsum.photos/400/300?random=4',
					factoryName: '测试工厂D',
					orderNumber: 'TEST004',
					uploadDate: new Date().toISOString(),
					fileSizeFormatted: '267.4 KB'
				}
			]
		}
	},
	
	methods: {
		toggleSelection(imageId) {
			const index = this.selectedImages.indexOf(imageId)
			if (index > -1) {
				this.selectedImages.splice(index, 1)
			} else {
				this.selectedImages.push(imageId)
			}
		},
		
		clearSelection() {
			this.selectedImages = []
		},
		
		getSelectedImageData() {
			return this.testImages.filter(img => this.selectedImages.includes(img.id))
				.map(img => ({
					id: img.id,
					imageName: img.name,
					imageUrl: img.url,
					factoryName: img.factoryName,
					orderNumber: img.orderNumber,
					uploadDate: img.uploadDate,
					fileSizeFormatted: img.fileSizeFormatted
				}))
		},
		
		async testSinglePrint() {
			try {
				uni.showLoading({ title: '准备打印...' })
				
				const images = this.getSelectedImageData()
				const settings = {
					printer: { name: '默认打印机', value: 'default' },
					paperSize: { name: 'A4', value: 'A4' },
					quality: { name: '标准质量', value: 'normal' },
					imagesPerPage: { name: '1张/页', value: 1 }
				}
				
				await PrintUtils.executePrint(images, settings)
				
				uni.hideLoading()
				uni.showToast({
					title: '打印任务已发送',
					icon: 'success'
				})
				
			} catch (error) {
				uni.hideLoading()
				uni.showToast({
					title: '打印失败: ' + error.message,
					icon: 'none',
					duration: 3000
				})
			}
		},
		
		async testBatchPrint() {
			try {
				uni.showLoading({ title: '准备批量打印...' })
				
				const images = this.getSelectedImageData()
				const settings = {
					printer: { name: '默认打印机', value: 'default' },
					paperSize: { name: 'A4', value: 'A4' },
					quality: { name: '标准质量', value: 'normal' },
					imagesPerPage: { name: '4张/页', value: 4 }
				}
				
				await PrintUtils.executePrint(images, settings)
				
				uni.hideLoading()
				uni.showToast({
					title: `${images.length}张图片打印任务已发送`,
					icon: 'success'
				})
				
			} catch (error) {
				uni.hideLoading()
				uni.showToast({
					title: '打印失败: ' + error.message,
					icon: 'none',
					duration: 3000
				})
			}
		}
	}
}
</script>

<style scoped>
.print-test {
	padding: 20px;
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30px;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.test-section {
	background: #ffffff;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
	display: block;
}

.test-images {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
	gap: 15px;
}

.test-image-item {
	border: 2px solid #eee;
	border-radius: 8px;
	padding: 10px;
	text-align: center;
	cursor: pointer;
	transition: all 0.3s ease;
	background: #f8f9fa;
}

.test-image-item:hover {
	border-color: #007aff;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
}

.test-image-item.selected {
	border-color: #007aff;
	background: #e3f2fd;
}

.test-image {
	width: 100%;
	height: 120px;
	object-fit: cover;
	border-radius: 6px;
	margin-bottom: 8px;
}

.image-name {
	font-size: 14px;
	color: #666;
	word-break: break-all;
}

.actions {
	display: flex;
	gap: 15px;
	justify-content: center;
	margin-bottom: 20px;
	flex-wrap: wrap;
}

.action-btn {
	padding: 12px 24px;
	background: #007aff;
	color: #ffffff;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: center;
	min-width: 120px;
}

.action-btn:hover {
	background: #0056cc;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.action-btn.secondary {
	background: #6c757d;
}

.action-btn.secondary:hover {
	background: #545b62;
}

.btn-text {
	font-size: 14px;
	font-weight: 500;
}

.info {
	background: #ffffff;
	border-radius: 12px;
	padding: 20px;
	text-align: center;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.info-text {
	display: block;
	color: #666;
	font-size: 14px;
	line-height: 1.6;
	margin-bottom: 8px;
}

.info-text:last-child {
	margin-bottom: 0;
}
</style>
