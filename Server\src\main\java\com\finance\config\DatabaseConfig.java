package com.finance.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 数据库配置类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Configuration
@EnableJpaRepositories(basePackages = "com.finance.repository")
public class DatabaseConfig implements CommandLineRunner {

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        testDatabaseConnection();
    }

    /**
     * 测试数据库连接
     */
    private void testDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            log.info("数据库连接测试成功！");
            log.info("数据库URL: {}", connection.getMetaData().getURL());
            log.info("数据库驱动: {}", connection.getMetaData().getDriverName());
            log.info("数据库版本: {}", connection.getMetaData().getDatabaseProductVersion());
        } catch (Exception e) {
            log.error("数据库连接测试失败: ", e);
            throw new RuntimeException("数据库连接失败", e);
        }
    }
}
