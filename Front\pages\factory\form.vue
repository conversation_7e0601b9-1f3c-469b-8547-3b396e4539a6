<template>
	<view class="container">
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">{{ isEdit ? '编辑工厂' : '新增工厂' }}</text>
			</view>
			
			<view class="form-content">
				<!-- 用户名 -->
				<view class="form-item">
					<text class="form-label">用户名 <text class="required">*</text></text>
					<input
						class="form-input"
						placeholder="请输入用户名"
						v-model="formData.username"
					/>
				</view>
				
				<!-- 密码 -->
				<view class="form-item">
					<text class="form-label">
						密码
						<text class="required" v-if="!isEdit">*</text>
						<text class="optional" v-if="isEdit">（留空则不修改）</text>
					</text>
					<view class="password-input-wrapper">
						<input
							class="form-input password-input"
							:type="showPassword ? 'text' : 'password'"
							placeholder="请输入密码"
							v-model="formData.password"
						/>
						<view class="password-toggle" @click="togglePasswordVisibility">
							<text class="eye-icon">{{ showPassword ? '🙈' : '👁' }}</text>
						</view>
					</view>
				</view>
				
				<!-- 工厂名称 -->
				<view class="form-item">
					<text class="form-label">工厂名称 <text class="required">*</text></text>
					<input 
						class="form-input"
						placeholder="请输入工厂名称"
						v-model="formData.factoryName"
					/>
				</view>
				
				<!-- 状态 -->
				<view class="form-item">
					<text class="form-label">状态 <text class="required">*</text></text>
					<picker 
						:value="statusIndex" 
						:range="statusOptions" 
						range-key="label"
						@change="onStatusChange"
					>
						<view class="picker-input">
							<text class="picker-text">{{ statusOptions[statusIndex].label }}</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
			</view>
			
			<view class="form-actions">
				<view class="action-btn cancel-btn" @click="handleCancel">
					<text class="btn-text">取消</text>
				</view>
				<view class="action-btn submit-btn" @click="handleSubmit">
					<text class="btn-text">{{ isEdit ? '更新' : '创建' }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import factoryApi from '@/api/factory.js'
import request from '@/utils/request.js'

export default {
	data() {
		return {
			isEdit: false,
			factoryId: null,
			formData: {
				username: '',
				password: '',
				factoryName: '',
				status: 1
			},
			statusOptions: [
				{ label: '启用', value: 1 },
				{ label: '禁用', value: 0 }
			],
			statusIndex: 0,
			submitting: false,
			showPassword: false
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.isEdit = true
			this.factoryId = parseInt(options.id)
			this.loadFactoryDetail()
		}
	},
	
	methods: {
		/**
		 * 加载工厂详情
		 */
		async loadFactoryDetail() {
			try {
				const response = await factoryApi.getFactoryById(this.factoryId)
				const factory = response.data
				
				this.formData = {
					username: factory.username,
					password: '', // 编辑时密码为空
					factoryName: factory.factoryName,
					status: factory.status
				}
				
				// 设置状态选择器
				this.statusIndex = this.statusOptions.findIndex(item => item.value === factory.status)
				
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			}
		},
		
		/**
		 * 状态选择变化
		 */
		onStatusChange(e) {
			this.statusIndex = e.detail.value
			this.formData.status = this.statusOptions[this.statusIndex].value
		},
		
		/**
		 * 表单验证
		 */
		validateForm() {
			if (!this.formData.username.trim()) {
				uni.showToast({
					title: '请输入用户名',
					icon: 'none'
				})
				return false
			}
			
			if (!this.isEdit && !this.formData.password.trim()) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				})
				return false
			}
			
			if (this.formData.password && this.formData.password.length < 6) {
				uni.showToast({
					title: '密码长度至少6位',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.factoryName.trim()) {
				uni.showToast({
					title: '请输入工厂名称',
					icon: 'none'
				})
				return false
			}
			
			return true
		},
		
		/**
		 * 提交表单
		 */
		async handleSubmit() {
			if (!this.validateForm() || this.submitting) {
				return
			}
			
			this.submitting = true
			
			try {
				const submitData = { ...this.formData }
				
				// 编辑时如果密码为空则不提交密码字段
				if (this.isEdit && !submitData.password) {
					delete submitData.password
				}
				
				if (this.isEdit) {
					await factoryApi.updateFactory(this.factoryId, submitData)
					uni.showToast({
						title: '更新成功',
						icon: 'success'
					})
				} else {
					await factoryApi.createFactory(submitData)
					uni.showToast({
						title: '创建成功',
						icon: 'success'
					})
				}

				// 清除工厂相关缓存
				this.clearFactoryCache()

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
				
			} catch (error) {
				uni.showToast({
					title: this.isEdit ? '更新失败' : '创建失败',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		},
		
		/**
		 * 切换密码可见性
		 */
		togglePasswordVisibility() {
			this.showPassword = !this.showPassword
		},

		/**
		 * 清除工厂相关缓存
		 */
		clearFactoryCache() {
			// 清除工厂列表相关的缓存
			request.clearCache('/factory')

			// 清除图片搜索页面可能缓存的工厂列表
			request.clearCache('/image/factories')
		},

		/**
		 * 取消操作
		 */
		handleCancel() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

view, input, text {
	box-sizing: border-box;
}

.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 15px;
	box-sizing: border-box;
	width: 100%;
	overflow-x: hidden;

	// PC端适配
	@media (min-width: 768px) {
		padding: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.form-card {
	background: #ffffff;
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	width: 100%;
	box-sizing: border-box;

	@media (min-width: 768px) {
		max-width: 500px;
		border-radius: 20px;
		box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
	}
}

.form-header {
	background: linear-gradient(135deg, #007AFF, #4DA6FF);
	padding: 20px;
	text-align: center;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
		animation: shimmer 3s ease-in-out infinite;
	}

	@media (min-width: 768px) {
		padding: 25px;
	}
}

@keyframes shimmer {
	0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
	50% { transform: translateX(0%) translateY(0%) rotate(180deg); }
}

.form-title {
	font-size: 18px;
	font-weight: 700;
	color: #ffffff;
	position: relative;
	z-index: 1;

	@media (min-width: 768px) {
		font-size: 22px;
	}
}

.form-content {
	padding: 20px;
	box-sizing: border-box;

	@media (min-width: 768px) {
		padding: 25px;
	}
}

.form-item {
	margin-bottom: 18px;
	width: 100%;
	overflow: hidden;

	@media (min-width: 768px) {
		margin-bottom: 22px;
	}
}

.form-label {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 6px;
	font-weight: 600;

	@media (min-width: 768px) {
		font-size: 16px;
		margin-bottom: 8px;
	}
}

.required {
	color: #ff4d4f;
}

.optional {
	color: #999;
	font-size: 14px;
	font-weight: 400;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.form-input {
	width: 100%;
	height: 40px;
	padding: 0 12px;
	border: 1px solid #e8e8e8;
	border-radius: 8px;
	font-size: 14px;
	color: #333;
	background: #ffffff;
	transition: all 0.3s ease;
	box-sizing: border-box;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	&:focus {
		border-color: #007AFF;
		box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
		outline: none;
	}

	&:disabled {
		background: #f5f5f5;
		color: #999;
		cursor: not-allowed;
	}

	&::placeholder {
		color: #bbb;
	}

	@media (min-width: 768px) {
		height: 44px;
		padding: 0 16px;
		border-radius: 10px;
		font-size: 16px;
	}
}

.password-input-wrapper {
	position: relative;
	width: 100%;
}

.password-input {
	padding-right: 45px !important;
}

.password-toggle {
	position: absolute;
	right: 12px;
	top: 50%;
	transform: translateY(-50%);
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	user-select: none;
	transition: all 0.2s ease;

	&:hover {
		opacity: 0.7;
	}

	&:active {
		transform: translateY(-50%) scale(0.95);
	}
}

.eye-icon {
	font-size: 16px;
	color: #666;
	line-height: 1;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 40px;
	padding: 0 12px;
	border: 1px solid #e8e8e8;
	border-radius: 8px;
	background: #ffffff;
	cursor: pointer;
	transition: all 0.3s ease;
	box-sizing: border-box;

	&:hover {
		border-color: #007AFF;
	}

	@media (min-width: 768px) {
		height: 44px;
		padding: 0 16px;
		border-radius: 10px;
	}
}

.picker-text {
	font-size: 14px;
	color: #333;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.picker-arrow {
	font-size: 12px;
	color: #999;
	transition: transform 0.3s ease;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.picker-input:hover .picker-arrow {
	transform: rotate(180deg);
}

.form-actions {
	display: flex;
	gap: 15px;
	padding: 20px;
	background: #f8f9fa;

	@media (min-width: 768px) {
		gap: 18px;
		padding: 25px;
	}
}

.action-btn {
	flex: 1;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	border: none;
	box-sizing: border-box;

	&:hover {
		transform: translateY(-1px);
	}

	@media (min-width: 768px) {
		height: 44px;
		border-radius: 10px;
		font-size: 16px;
	}
}

.cancel-btn {
	background: #f5f5f5;
	color: #666;

	&:hover {
		background: #e9ecef;
		color: #333;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}
}

.submit-btn {
	background: linear-gradient(135deg, #007AFF, #0056CC);
	color: #ffffff;
	box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);

	&:hover {
		box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
	}

	&:disabled {
		background: #ccc;
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
	}
}

.btn-text {
	font-weight: 600;
}
</style>
