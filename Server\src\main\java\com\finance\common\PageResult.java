package com.finance.common;

import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页响应结果类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class PageResult<T> {
    
    /**
     * 当前页数据
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer current;
    
    /**
     * 每页大小
     */
    private Integer size;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    public PageResult() {}
    
    public PageResult(List<T> records, Long total, Integer current, Integer size) {
        this.records = records;
        this.total = total;
        this.current = current;
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    /**
     * 从Spring Data Page对象构建分页结果
     */
    public static <T> PageResult<T> of(Page<T> page) {
        return new PageResult<>(
            page.getContent(),
            page.getTotalElements(),
            page.getNumber() + 1, // Spring Data页码从0开始，转换为从1开始
            page.getSize()
        );
    }
    
    /**
     * 构建空的分页结果
     */
    public static <T> PageResult<T> empty(Integer current, Integer size) {
        return new PageResult<>(new ArrayList<>(), 0L, current, size);
    }
}
