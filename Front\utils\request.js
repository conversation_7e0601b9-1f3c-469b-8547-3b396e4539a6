/**
 * HTTP请求工具
 */
import config from '@/config/api.js'

class Request {
  constructor() {
    this.baseURL = config.baseURL
    this.timeout = config.timeout
    this.cache = new Map() // 请求缓存
    this.pendingRequests = new Map() // 防止重复请求
    this.maxCacheSize = 100 // 最大缓存数量
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 发送请求
   */
  request(options = {}) {
    return new Promise((resolve, reject) => {
      // 默认配置
      const defaultOptions = {
        url: '',
        method: 'GET',
        data: {},
        header: {
          'Content-Type': 'application/json'
        },
        timeout: this.timeout,
        cache: true // 默认启用缓存
      }

      // 合并配置
      const requestOptions = Object.assign({}, defaultOptions, options)

      // 处理URL
      if (!requestOptions.url.startsWith('http')) {
        requestOptions.url = this.baseURL + requestOptions.url
      }

      // 生成缓存key
      const cacheKey = this.generateCacheKey(requestOptions)

      // 检查缓存（仅GET请求且启用缓存）
      if (requestOptions.method === 'GET' && requestOptions.cache) {
        const cachedData = this.getCache(cacheKey)
        if (cachedData) {
          return resolve(cachedData)
        }
      }

      // 防止重复请求
      if (this.pendingRequests.has(cacheKey)) {
        return this.pendingRequests.get(cacheKey)
      }

      // 请求拦截器
      this.requestInterceptor(requestOptions)

      // 创建请求Promise
      const requestPromise = new Promise((res, rej) => {
        uni.request({
          ...requestOptions,
          success: (response) => {
            // 响应拦截器
            this.responseInterceptor(response, res, rej, cacheKey, requestOptions)
          },
          fail: (error) => {
            this.handleError(error, rej)
          }
        })
      })

      // 存储pending请求
      this.pendingRequests.set(cacheKey, requestPromise)

      // 请求完成后清除pending
      requestPromise.finally(() => {
        this.pendingRequests.delete(cacheKey)
      })

      requestPromise.then(resolve).catch(reject)
    })
  }

  /**
   * 请求拦截器
   */
  requestInterceptor(options) {
    // 显示加载提示
    uni.showLoading({
      title: '加载中...',
      mask: true
    })

    // 可以在这里添加token等认证信息
    // options.header.Authorization = 'Bearer ' + getToken()
  }

  /**
   * 响应拦截器
   */
  responseInterceptor(response, resolve, reject, cacheKey, requestOptions) {
    // 隐藏加载提示
    uni.hideLoading()

    const { data, statusCode } = response

    if (statusCode === 200) {
      if (data.code === 200) {
        // 缓存GET请求的成功响应
        if (requestOptions.method === 'GET' && requestOptions.cache) {
          this.setCache(cacheKey, data)
        }
        resolve(data)
      } else {
        this.showError(data.message || '请求失败')
        reject(data)
      }
    } else {
      this.showError(`请求失败，状态码：${statusCode}`)
      reject(response)
    }
  }

  /**
   * 错误处理
   */
  handleError(error, reject) {
    uni.hideLoading()
    
    let message = '网络请求失败'
    
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        message = '请求超时'
      } else if (error.errMsg.includes('fail')) {
        message = '网络连接失败'
      }
    }
    
    this.showError(message)
    reject(error)
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * GET请求
   */
  get(url, data = {}) {
    return this.request({
      url,
      method: 'GET',
      data
    })
  }

  /**
   * POST请求
   */
  post(url, data = {}) {
    return this.request({
      url,
      method: 'POST',
      data
    })
  }

  /**
   * PUT请求
   */
  put(url, data = {}) {
    return this.request({
      url,
      method: 'PUT',
      data
    })
  }

  /**
   * DELETE请求
   */
  delete(url, data = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      cache: false // DELETE请求不缓存
    })
  }

  /**
   * 生成缓存key
   */
  generateCacheKey(options) {
    const { url, method, data } = options
    return `${method}:${url}:${JSON.stringify(data)}`
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    // 过期缓存删除
    if (cached) {
      this.cache.delete(key)
    }
    return null
  }

  /**
   * 设置缓存
   */
  setCache(key, data) {
    // 如果缓存已满，删除最旧的
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 清除缓存
   */
  clearCache(pattern) {
    if (pattern) {
      // 清除匹配模式的缓存
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear()
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 创建实例
const request = new Request()

export default request
