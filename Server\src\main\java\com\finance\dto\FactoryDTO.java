package com.finance.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 工厂数据传输对象
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class FactoryDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    private String username;

    /**
     * 密码（明文，用于新增和修改）
     */
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    /**
     * 工厂名称
     */
    @NotBlank(message = "工厂名称不能为空")
    @Size(max = 100, message = "工厂名称长度不能超过100个字符")
    private String factoryName;

    /**
     * 状态：1-启用，0-禁用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
