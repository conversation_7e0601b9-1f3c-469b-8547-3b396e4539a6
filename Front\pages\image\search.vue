<template>
	<view class="container">
		<!-- 搜索条件区域 -->
		<view class="search-section">
			<view class="search-card">
				<view class="search-title">
					<text class="title-text">图片检索</text>
					<view class="toggle-btn" @click="toggleSearchForm">
						<text class="toggle-text">{{ showSearchForm ? '收起' : '展开' }}</text>
						<text class="toggle-icon">{{ showSearchForm ? '▲' : '▼' }}</text>
					</view>
				</view>
				
				<view class="search-form" v-show="showSearchForm">
					<!-- 第一行：工厂选择、订单号和时间范围 -->
					<view class="search-filters-row">
						<!-- 工厂选择 -->
						<view class="form-row">
							<text class="form-label">工厂名称</text>
							<picker
								:value="factoryIndex"
								:range="factoryOptions"
								range-key="label"
								@change="onFactoryChange"
							>
								<view class="picker-input">
									<text class="picker-text">{{ factoryOptions[factoryIndex].label }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>

						<!-- 订单号输入 -->
						<view class="form-row">
							<text class="form-label">订单号</text>
							<input
								class="form-input"
								placeholder="请输入订单号"
								v-model="searchQuery.orderNumber"
							/>
						</view>

						<!-- 时间范围 -->
						<view class="form-row">
							<text class="form-label">时间范围</text>
							<view class="date-range">
								<picker
									mode="date"
									:value="searchQuery.startDate"
									@change="onStartDateChange"
								>
									<view class="date-input">
										<text class="date-text">{{ searchQuery.startDate || '开始日期' }}</text>
									</view>
								</picker>
								<text class="date-separator">至</text>
								<picker
									mode="date"
									:value="searchQuery.endDate"
									@change="onEndDateChange"
								>
									<view class="date-input">
										<text class="date-text">{{ searchQuery.endDate || '结束日期' }}</text>
									</view>
								</picker>
							</view>
						</view>
					</view>

					<!-- 第二行：搜索按钮 -->
					<view class="search-actions">
						<view class="action-btn reset-btn" @click="resetSearch">
							<text class="btn-text">重置</text>
						</view>
						<view class="action-btn search-btn" @click="handleSearch">
							<text class="btn-text">搜索</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 结果统计 -->
		<view class="stats-bar">
			<view class="stats-info">
				<text class="stats-text">共找到 {{ pageData.total }} 张图片</text>
				<text class="page-stats" v-if="pageData.total > 0">
					显示第 {{ (pageData.current - 1) * pageData.size + 1 }}-{{ Math.min(pageData.current * pageData.size, pageData.total) }} 张
				</text>
			</view>
			<view class="action-buttons">
				<!-- 批量操作按钮 -->
				<view class="batch-actions" v-if="selectedImages.length > 0">
					<text class="selected-count">已选择 {{ selectedImages.length }} 张</text>
					<view class="action-btn print-btn" @click="showPrintModal">
						<text class="btn-icon">🖨️</text>
						<text class="btn-text">批量打印</text>
					</view>
					<view class="action-btn clear-btn" @click="clearSelection">
						<text class="btn-icon">✕</text>
						<text class="btn-text">清空选择</text>
					</view>
				</view>
				<!-- 视图模式切换 -->
				<view class="view-mode">
					<view
						class="mode-btn"
						:class="{ active: viewMode === 'grid' }"
						@click="setViewMode('grid')"
					>
						<text class="mode-icon">⊞</text>
					</view>
					<view
						class="mode-btn"
						:class="{ active: viewMode === 'list' }"
						@click="setViewMode('list')"
					>
						<text class="mode-icon">☰</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载骨架屏 -->
		<view v-if="loading && imageList.length === 0" class="skeleton-container">
			<view class="image-list" :class="viewMode">
				<SkeletonLoader
					v-for="n in 12"
					:key="n"
					type="card"
					class="skeleton-item"
				/>
			</view>
		</view>

		<!-- 图片列表 -->
		<view v-else class="image-list" :class="viewMode">
			<view
				class="image-item"
				v-for="image in visibleImageList"
				:key="image.id"
				:class="{ selected: isImageSelected(image.id) }"
			>
				<!-- 选择复选框 -->
				<view class="image-checkbox" @click.stop="toggleImageSelection(image)">
					<view class="checkbox" :class="{ checked: isImageSelected(image.id) }">
						<text class="check-icon" v-if="isImageSelected(image.id)">✓</text>
					</view>
				</view>

				<view class="image-wrapper" @click="previewImage(image)">
					<image
						class="image-thumb"
						:src="image.imageUrl"
						mode="aspectFill"
						:lazy-load="true"
						:show-loading="true"
						:show-error="true"
						@load="onImageLoad"
						@error="onImageError"
					/>
					<view class="image-overlay">
						<text class="overlay-icon">🔍</text>
					</view>
				</view>
				
				<view class="image-info">
					<text class="image-name">{{ image.imageName }}</text>
					<text class="image-factory">{{ image.factoryName }}</text>
					<text class="image-order">{{ image.orderNumber }}</text>
					<text class="image-size">{{ image.fileSizeFormatted }}</text>
					<text class="image-date">{{ formatDate(image.uploadDate) }}</text>
				</view>
			</view>
		</view>

		<!-- 分页控件 -->
		<view class="pagination" v-if="pageData.pages > 1">
			<view class="pagination-info">
				<text class="page-info">第 {{ pageData.current }} 页，共 {{ pageData.pages }} 页</text>
			</view>
			<view class="pagination-controls">
				<view
					class="page-btn"
					:class="{ disabled: pageData.current === 1 }"
					@click="goToPage(1)"
				>
					<text class="btn-text">首页</text>
				</view>
				<view
					class="page-btn"
					:class="{ disabled: pageData.current === 1 }"
					@click="goToPage(pageData.current - 1)"
				>
					<text class="btn-text">上一页</text>
				</view>

				<!-- 页码按钮 -->
				<view class="page-numbers">
					<view
						v-for="page in getPageNumbers()"
						:key="page"
						class="page-number"
						:class="{ active: page === pageData.current, ellipsis: page === '...' }"
						@click="page !== '...' ? goToPage(page) : null"
					>
						<text class="number-text">{{ page }}</text>
					</view>
				</view>

				<view
					class="page-btn"
					:class="{ disabled: pageData.current === pageData.pages }"
					@click="goToPage(pageData.current + 1)"
				>
					<text class="btn-text">下一页</text>
				</view>
				<view
					class="page-btn"
					:class="{ disabled: pageData.current === pageData.pages }"
					@click="goToPage(pageData.pages)"
				>
					<text class="btn-text">末页</text>
				</view>
			</view>

			<!-- 快速跳转 -->
			<view class="quick-jump" v-if="pageData.pages > 10">
				<text class="jump-label">跳转到</text>
				<input
					class="jump-input"
					type="number"
					:value="jumpPage"
					@input="onJumpPageInput"
					@confirm="quickJump"
					placeholder="页码"
					:max="pageData.pages"
					:min="1"
				/>
				<text class="jump-label">页</text>
				<view class="jump-btn" @click="quickJump">
					<text class="jump-text">跳转</text>
				</view>
			</view>
		</view>

		<!-- 无数据提示 -->
		<view class="empty-state" v-if="!loading && imageList.length === 0">
			<text class="empty-icon">🖼️</text>
			<text class="empty-text">暂无图片数据</text>
			<text class="empty-desc">请调整搜索条件后重试</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 图片预览模态框 -->
		<view class="preview-modal" v-if="showPreview" @click="closePreview">
			<view class="preview-content" @click.stop>
				<view class="preview-header">
					<text class="preview-title">{{ currentImage.imageName }}</text>
					<view class="preview-controls">
						<view class="control-btn" @click="rotateLeft" title="向左旋转">
							<text class="control-icon">↺</text>
						</view>
						<view class="control-btn" @click="rotateRight" title="向右旋转">
							<text class="control-icon">↻</text>
						</view>
						<view class="control-btn" @click="zoomOut(0.1)" title="缩小">
							<text class="control-icon">-</text>
						</view>
						<view class="control-btn" @click="zoomIn(0.1)" title="放大">
							<text class="control-icon">+</text>
						</view>
						<view class="control-btn" @click="resetImage" title="重置">
							<text class="control-icon">⟲</text>
						</view>
						<view class="control-btn print-single-btn" @click="printSingleImage" title="打印">
							<text class="control-icon">🖨️</text>
						</view>
					</view>
					<view class="close-btn" @click="closePreview">
						<text class="close-icon">✕</text>
					</view>
				</view>

				<view
					class="preview-image-wrapper"
					@mousedown="startDrag"
					@mousemove="onDrag"
					@mouseup="endDrag"
					@mouseleave="endDrag"
					@dblclick="toggleZoom"
					ref="imageWrapper"
					:style="{ cursor: isDragging ? 'grabbing' : 'grab' }"
				>
					<view
						class="image-container"
						:style="{
							transform: `translate(${imageOffsetX}px, ${imageOffsetY}px) scale(${imageScale}) rotate(${imageRotation}deg)`,
							transition: isDragging ? 'none' : imageTransition
						}"
					>
						<image
							class="preview-image"
							:src="currentImage.imageUrl"
							mode="aspectFit"
							@load="onPreviewImageLoad"
						/>
					</view>
				</view>

				<view class="preview-info">
					<view class="info-row">
						<view class="info-item">
							<text class="info-label">工厂：</text>
							<text class="info-value">{{ currentImage.factoryName }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">订单号：</text>
							<text class="info-value">{{ currentImage.orderNumber }}</text>
						</view>
					</view>
					<view class="info-row">
						<view class="info-item">
							<text class="info-label">文件大小：</text>
							<text class="info-value">{{ currentImage.fileSizeFormatted }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">上传时间：</text>
							<text class="info-value">{{ formatDateTime(currentImage.uploadDate) }}</text>
						</view>
					</view>

				</view>
			</view>
		</view>

		<!-- 打印模态框 -->
		<view class="print-modal" v-if="showPrintDialog" @click="closePrintModal">
			<view class="print-content" @click.stop>
				<view class="print-header">
					<text class="print-title">打印设置</text>
					<view class="close-btn" @click="closePrintModal">
						<text class="close-icon">✕</text>
					</view>
				</view>

				<view class="print-body">
					<!-- 打印预览 -->
					<view class="print-preview">
						<text class="preview-title">打印预览 ({{ printImages.length }} 张图片)</text>
						<view class="preview-grid">
							<view
								class="preview-item"
								v-for="image in printImages.slice(0, 6)"
								:key="image.id"
							>
								<image class="preview-thumb" :src="image.imageUrl" mode="aspectFit" />
								<text class="preview-name">{{ image.imageName }}</text>
							</view>
							<view class="preview-more" v-if="printImages.length > 6">
								<text class="more-text">还有 {{ printImages.length - 6 }} 张...</text>
							</view>
						</view>
					</view>

					<!-- 打印设置 -->
					<view class="print-settings">
						<view class="setting-group">
							<text class="setting-label">打印机</text>
							<picker
								:value="printerIndex"
								:range="printerOptions"
								range-key="name"
								@change="onPrinterChange"
							>
								<view class="picker-input">
									<text class="picker-text">{{ printerOptions[printerIndex].name }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>

						<view class="setting-group">
							<text class="setting-label">纸张大小</text>
							<picker
								:value="paperSizeIndex"
								:range="paperSizeOptions"
								range-key="name"
								@change="onPaperSizeChange"
							>
								<view class="picker-input">
									<text class="picker-text">{{ paperSizeOptions[paperSizeIndex].name }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>

						<view class="setting-group">
							<text class="setting-label">打印质量</text>
							<picker
								:value="qualityIndex"
								:range="qualityOptions"
								range-key="name"
								@change="onQualityChange"
							>
								<view class="picker-input">
									<text class="picker-text">{{ qualityOptions[qualityIndex].name }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>

						<view class="setting-group">
							<text class="setting-label">每页图片数</text>
							<picker
								:value="imagesPerPageIndex"
								:range="imagesPerPageOptions"
								range-key="name"
								@change="onImagesPerPageChange"
							>
								<view class="picker-input">
									<text class="picker-text">{{ imagesPerPageOptions[imagesPerPageIndex].name }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>
					</view>
				</view>

				<view class="print-footer">
					<view class="footer-btn cancel-btn" @click="closePrintModal">
						<text class="btn-text">取消</text>
					</view>
					<view class="footer-btn confirm-btn" @click="confirmPrint">
						<text class="btn-text">开始打印</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import imageApi from '@/api/image.js'
import PrintUtils from '@/utils/printUtils.js'
import SkeletonLoader from '@/components/SkeletonLoader.vue'
import performanceOptimizer from '@/utils/performance.js'
import request from '@/utils/request.js'

export default {
	components: {
		SkeletonLoader
	},
	data() {
		return {
			showSearchForm: true,
			viewMode: 'grid', // grid 或 list
			imageList: [],
			pageData: {
				current: 1,
				size: 12, // 每页显示12张图片
				total: 0,
				pages: 0
			},
			searchQuery: {
				factoryName: '',
				orderNumber: '',
				startDate: '',
				endDate: ''
			},
			factoryOptions: [
				{ label: '全部工厂', value: '' }
			],
			factoryIndex: 0,
			loading: false,
			showPreview: false,
			currentImage: {},
			// 图片预览控制
			imageScale: 1,
			imageRotation: 0,
			imageTransition: 'transform 0.3s ease',
			minScale: 0.5,
			maxScale: 3,
			wheelListenerAdded: false,
			// 拖拽控制
			isDragging: false,
			dragStartX: 0,
			dragStartY: 0,
			imageOffsetX: 0,
			imageOffsetY: 0,
			lastOffsetX: 0,
			lastOffsetY: 0,
			wheelListenerAdded: false,
			// 图片加载控制
			imageLoadingStates: {}, // 图片加载状态

			// 性能优化相关
			debounceTimer: null, // 通用防抖定时器
			virtualScroll: {
				enabled: false, // 当数据量大时启用
				itemHeight: 220, // 每个图片项的高度(rpx)
				visibleCount: 20, // 可见项数量
				startIndex: 0,
				endIndex: 20
			},
			// 快速跳转
			jumpPage: '', // 跳转页码
			// 批量选择和打印
			selectedImages: [], // 选中的图片
			showPrintDialog: false, // 显示打印对话框
			printImages: [], // 要打印的图片
			// 打印设置
			printerIndex: 0,
			printerOptions: [
				{ name: '默认打印机', value: 'default' },
				{ name: 'Microsoft Print to PDF', value: 'pdf' },
				{ name: 'Microsoft XPS Document Writer', value: 'xps' }
			],
			paperSizeIndex: 0,
			paperSizeOptions: [
				{ name: 'A4 (210 × 297 mm)', value: 'A4' },
				{ name: 'A3 (297 × 420 mm)', value: 'A3' },
				{ name: 'Letter (216 × 279 mm)', value: 'Letter' },
				{ name: '4×6 英寸', value: '4x6' }
			],
			qualityIndex: 1,
			qualityOptions: [
				{ name: '草稿质量', value: 'draft' },
				{ name: '标准质量', value: 'normal' },
				{ name: '高质量', value: 'high' },
				{ name: '最佳质量', value: 'best' }
			],
			imagesPerPageIndex: 0,
			imagesPerPageOptions: [
				{ name: '1张/页', value: 1 },
				{ name: '2张/页', value: 2 },
				{ name: '4张/页', value: 4 },
				{ name: '6张/页', value: 6 },
				{ name: '9张/页', value: 9 }
			]
		}
	},
	
	onLoad() {
		this.loadFactoryOptions()
		this.loadImageList()
	},

	onShow() {
		// 页面显示时重新加载工厂选项（可能有新增或删除）
		// 清除工厂列表缓存确保获取最新数据
		request.clearCache('/image/factories')
		this.loadFactoryOptions()
	},

	async mounted() {
		// 添加键盘事件监听
		document.addEventListener('keydown', this.handleKeydown)
		this.addWheelListener()
		// 初始化打印机列表
		await this.initPrinters()
	},

	beforeDestroy() {
		// 移除键盘事件监听
		document.removeEventListener('keydown', this.handleKeydown)
		this.removeWheelListener()
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.refreshList()
	},

	computed: {
		/**
		 * 虚拟滚动显示的图片列表
		 */
		visibleImageList() {
			if (!this.virtualScroll.enabled || this.imageList.length <= 50) {
				return this.imageList
			}

			return this.imageList.slice(
				this.virtualScroll.startIndex,
				this.virtualScroll.endIndex
			)
		},

		/**
		 * 是否启用虚拟滚动
		 */
		shouldUseVirtualScroll() {
			return this.imageList.length > 100
		}
	},

	methods: {


		/**
		 * 加载工厂选项
		 */
		async loadFactoryOptions() {
			try {
				const response = await imageApi.getFactoryNames()
				const factories = response.data || []
				
				this.factoryOptions = [
					{ label: '全部工厂', value: '' },
					...factories.map(name => ({ label: name, value: name }))
				]
			} catch (error) {
				// 加载工厂列表失败
			}
		},
		
		/**
		 * 加载图片列表
		 */
		async loadImageList(page = null) {
			if (this.loading) return

			this.loading = true

			try {
				const params = {
					current: page || this.pageData.current,
					size: this.pageData.size,
					...this.buildSearchParams()
				}

				const response = await imageApi.getImagePage(params)
				const { data } = response

				this.imageList = data.records
				this.pageData = data

				// 检查是否需要启用虚拟滚动
				this.updateVirtualScroll()

			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				uni.stopPullDownRefresh()
			}
		},

		/**
		 * 更新虚拟滚动配置
		 */
		updateVirtualScroll() {
			if (this.shouldUseVirtualScroll) {
				this.virtualScroll.enabled = true
				this.virtualScroll.endIndex = Math.min(
					this.virtualScroll.visibleCount,
					this.imageList.length
				)
			} else {
				this.virtualScroll.enabled = false
			}
		},

		/**
		 * 防抖搜索
		 */
		debounceSearch: performanceOptimizer.debounce(function() {
			this.handleSearch()
		}, 300),



		/**
		 * 构建搜索参数
		 */
		buildSearchParams() {
			const params = {}
			
			if (this.searchQuery.factoryName) {
				params.factoryName = this.searchQuery.factoryName
			}
			
			if (this.searchQuery.orderNumber) {
				params.orderNumber = this.searchQuery.orderNumber
			}
			
			if (this.searchQuery.startDate) {
				params.startDate = this.searchQuery.startDate
			}
			
			if (this.searchQuery.endDate) {
				params.endDate = this.searchQuery.endDate
			}
			
			return params
		},
		
		/**
		 * 切换搜索表单显示
		 */
		toggleSearchForm() {
			this.showSearchForm = !this.showSearchForm
		},
		
		/**
		 * 工厂选择变化
		 */
		onFactoryChange(e) {
			this.factoryIndex = e.detail.value
			this.searchQuery.factoryName = this.factoryOptions[this.factoryIndex].value
		},
		
		/**
		 * 开始日期变化
		 */
		onStartDateChange(e) {
			this.searchQuery.startDate = e.detail.value
		},
		
		/**
		 * 结束日期变化
		 */
		onEndDateChange(e) {
			this.searchQuery.endDate = e.detail.value
		},
		
		/**
		 * 执行搜索
		 */
		handleSearch() {
			this.showSearchForm = false
			this.refreshList()
		},
		
		/**
		 * 重置搜索
		 */
		resetSearch() {
			this.searchQuery = {
				factoryName: '',
				orderNumber: '',
				imageName: '',
				startDate: '',
				endDate: ''
			}
			this.factoryIndex = 0
			this.refreshList()
		},
		
		/**
		 * 刷新列表
		 */
		refreshList() {
			this.pageData.current = 1
			this.loadImageList()
		},
		
		/**
		 * 跳转到指定页面
		 */
		goToPage(page) {
			if (page < 1 || page > this.pageData.pages || page === this.pageData.current || this.loading) {
				return
			}

			this.pageData.current = page
			this.loadImageList(page)

			// 滚动到顶部
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 300
			})
		},

		/**
		 * 获取页码数组
		 */
		getPageNumbers() {
			const current = this.pageData.current
			const total = this.pageData.pages
			const pages = []

			// 移动端显示较少页码，PC端显示更多
			const isMobile = uni.getSystemInfoSync().windowWidth < 768
			const maxPages = isMobile ? 5 : 7

			if (total <= maxPages) {
				// 总页数小于等于最大显示数，显示所有页码
				for (let i = 1; i <= total; i++) {
					pages.push(i)
				}
			} else {
				// 总页数大于最大显示数，显示省略号
				const showPages = isMobile ? 3 : 5
				const halfShow = Math.floor(showPages / 2)

				if (current <= halfShow + 1) {
					// 当前页在前面
					for (let i = 1; i <= showPages; i++) {
						pages.push(i)
					}
					pages.push('...')
					pages.push(total)
				} else if (current >= total - halfShow) {
					// 当前页在后面
					pages.push(1)
					pages.push('...')
					for (let i = total - showPages + 1; i <= total; i++) {
						pages.push(i)
					}
				} else {
					// 当前页在中间
					pages.push(1)
					pages.push('...')
					for (let i = current - halfShow; i <= current + halfShow; i++) {
						pages.push(i)
					}
					pages.push('...')
					pages.push(total)
				}
			}

			return pages
		},

		/**
		 * 快速跳转页面输入
		 */
		onJumpPageInput(e) {
			this.jumpPage = e.detail.value
		},

		/**
		 * 快速跳转
		 */
		quickJump() {
			const page = parseInt(this.jumpPage)
			if (page && page >= 1 && page <= this.pageData.pages) {
				this.goToPage(page)
				this.jumpPage = ''
			} else {
				uni.showToast({
					title: `请输入1-${this.pageData.pages}之间的页码`,
					icon: 'none'
				})
			}
		},
		
		/**
		 * 设置视图模式
		 */
		setViewMode(mode) {
			this.viewMode = mode
		},
		
		/**
		 * 预览图片
		 */
		previewImage(image) {
			this.currentImage = image
			this.showPreview = true
			this.resetImageControls()
			// 延迟添加滚轮监听，确保DOM已渲染
			this.$nextTick(() => {
				this.addWheelListener()
			})
		},

		/**
		 * 关闭预览
		 */
		closePreview() {
			this.removeWheelListener()
			this.showPreview = false
			this.currentImage = {}
			this.resetImageControls()
		},

		/**
		 * 重置图片控制状态
		 */
		resetImageControls() {
			this.imageScale = 1
			this.imageRotation = 0
			this.imageTransition = 'transform 0.3s ease'
			this.imageOffsetX = 0
			this.imageOffsetY = 0
			this.lastOffsetX = 0
			this.lastOffsetY = 0
			this.isDragging = false
		},

		/**
		 * 向左旋转
		 */
		rotateLeft() {
			this.imageRotation -= 90
		},

		/**
		 * 向右旋转
		 */
		rotateRight() {
			this.imageRotation += 90
		},

		/**
		 * 放大图片
		 */
		zoomIn(step = 0.2) {
			if (this.imageScale < this.maxScale) {
				this.imageScale = Math.min(this.imageScale + step, this.maxScale)
			}
		},

		/**
		 * 缩小图片
		 */
		zoomOut(step = 0.2) {
			if (this.imageScale > this.minScale) {
				this.imageScale = Math.max(this.imageScale - step, this.minScale)

				// 如果缩放到1或更小，重置偏移量
				if (this.imageScale <= 1) {
					this.imageOffsetX = 0
					this.imageOffsetY = 0
					this.lastOffsetX = 0
					this.lastOffsetY = 0
				}
			}
		},

		/**
		 * 重置图片
		 */
		resetImage() {
			this.resetImageControls()
		},

		/**
		 * 开始拖拽
		 */
		startDrag(e) {
			e.preventDefault()
			this.isDragging = true
			this.dragStartX = e.clientX
			this.dragStartY = e.clientY
			this.lastOffsetX = this.imageOffsetX
			this.lastOffsetY = this.imageOffsetY
		},

		/**
		 * 拖拽中
		 */
		onDrag(e) {
			if (!this.isDragging) return

			e.preventDefault()
			const deltaX = e.clientX - this.dragStartX
			const deltaY = e.clientY - this.dragStartY

			this.imageOffsetX = this.lastOffsetX + deltaX
			this.imageOffsetY = this.lastOffsetY + deltaY
		},

		/**
		 * 结束拖拽
		 */
		endDrag() {
			this.isDragging = false
		},

		/**
		 * 切换缩放（通过双击或按钮控制）
		 */
		toggleZoom() {
			this.imageTransition = 'transform 0.3s ease'

			if (this.imageScale === 1) {
				this.imageScale = 2
			} else {
				this.imageScale = 1
				// 缩小时重置偏移量
				this.imageOffsetX = 0
				this.imageOffsetY = 0
				this.lastOffsetX = 0
				this.lastOffsetY = 0
			}
		},

		/**
		 * 图片加载完成
		 */
		onImageLoad() {
			// 图片加载完成后的处理
		},

		/**
		 * 预览图片加载完成
		 */
		onPreviewImageLoad() {
			// 预览图片加载完成后的处理
		},

		/**
		 * 添加滚轮事件监听
		 */
		addWheelListener() {
			this.$nextTick(() => {
				const imageWrapper = document.querySelector('.preview-image-wrapper')
				if (imageWrapper && this.showPreview) {
					// 移除可能存在的旧监听器
					this.removeWheelListener()
					// 添加新的监听器
					imageWrapper.addEventListener('wheel', this.handleWheelNative, { passive: false })
					imageWrapper.addEventListener('mousewheel', this.handleWheelNative, { passive: false })
					imageWrapper.addEventListener('DOMMouseScroll', this.handleWheelNative, { passive: false })
					this.wheelListenerAdded = true
				}
			})
		},

		/**
		 * 移除滚轮事件监听
		 */
		removeWheelListener() {
			if (this.wheelListenerAdded) {
				const imageWrapper = document.querySelector('.preview-image-wrapper')
				if (imageWrapper) {
					imageWrapper.removeEventListener('wheel', this.handleWheelNative)
					imageWrapper.removeEventListener('mousewheel', this.handleWheelNative)
					imageWrapper.removeEventListener('DOMMouseScroll', this.handleWheelNative)
					this.wheelListenerAdded = false
				}
			}
		},

		/**
		 * 原生滚轮事件处理
		 */
		handleWheelNative(event) {
			if (!this.showPreview) return

			event.preventDefault()
			event.stopPropagation()

			let delta = 0

			// 兼容不同浏览器的滚轮事件
			if (event.deltaY !== undefined) {
				// 标准的wheel事件 (现代浏览器)
				delta = -event.deltaY
			} else if (event.wheelDelta !== undefined) {
				// Chrome, Safari, Opera (旧版本)
				delta = event.wheelDelta
			} else if (event.detail !== undefined) {
				// Firefox (旧版本)
				delta = -event.detail * 40 // Firefox的detail值较小，需要放大
			}

			// 滚轮事件处理

			// 确保delta有值
			if (delta !== 0) {
				if (delta > 0) {
					// 向上滚动，放大
					this.zoomIn(0.1)
				} else {
					// 向下滚动，缩小
					this.zoomOut(0.1)
				}
			}
		},

		/**
		 * 键盘事件处理
		 */
		handleKeydown(event) {
			if (!this.showPreview) return

			switch (event.key) {
				case 'Escape':
					this.closePreview()
					break
				case 'ArrowLeft':
					event.preventDefault()
					this.rotateLeft()
					break
				case 'ArrowRight':
					event.preventDefault()
					this.rotateRight()
					break
				case '0':
					event.preventDefault()
					this.resetImage()
					break
			}
		},
		
		/**
		 * 图片加载错误处理
		 */
		onImageError() {
			// 图片加载失败处理
		},

		// ==================== 批量选择和打印功能 ====================

		/**
		 * 切换图片选择状态
		 */
		toggleImageSelection(image) {
			const index = this.selectedImages.findIndex(item => item.id === image.id)
			if (index > -1) {
				this.selectedImages.splice(index, 1)
			} else {
				this.selectedImages.push(image)
			}
		},

		/**
		 * 检查图片是否被选中
		 */
		isImageSelected(imageId) {
			return this.selectedImages.some(item => item.id === imageId)
		},

		/**
		 * 清空选择
		 */
		clearSelection() {
			this.selectedImages = []
		},

		/**
		 * 显示打印模态框
		 */
		showPrintModal() {
			if (this.selectedImages.length === 0) {
				uni.showToast({
					title: '请先选择要打印的图片',
					icon: 'none'
				})
				return
			}
			this.printImages = [...this.selectedImages]
			this.showPrintDialog = true
		},

		/**
		 * 单张图片打印
		 */
		printSingleImage() {
			this.printImages = [this.currentImage]
			this.showPrintDialog = true
		},

		/**
		 * 关闭打印模态框
		 */
		closePrintModal() {
			this.showPrintDialog = false
			this.printImages = []
		},

		/**
		 * 打印机选择改变
		 */
		onPrinterChange(e) {
			this.printerIndex = e.detail.value
		},

		/**
		 * 纸张大小改变
		 */
		onPaperSizeChange(e) {
			this.paperSizeIndex = e.detail.value
		},

		/**
		 * 打印质量改变
		 */
		onQualityChange(e) {
			this.qualityIndex = e.detail.value
		},

		/**
		 * 每页图片数改变
		 */
		onImagesPerPageChange(e) {
			this.imagesPerPageIndex = e.detail.value
		},

		/**
		 * 确认打印
		 */
		async confirmPrint() {
			try {
				uni.showLoading({
					title: '准备打印...'
				})

				// 获取打印设置
				const printSettings = {
					printer: this.printerOptions[this.printerIndex],
					paperSize: this.paperSizeOptions[this.paperSizeIndex],
					quality: this.qualityOptions[this.qualityIndex],
					imagesPerPage: this.imagesPerPageOptions[this.imagesPerPageIndex]
				}

				// 调用打印功能
				await this.executePrint(this.printImages, printSettings)

				uni.hideLoading()
				uni.showToast({
					title: '打印任务已发送',
					icon: 'success'
				})

				this.closePrintModal()
				this.clearSelection()

			} catch (error) {
				uni.hideLoading()
				uni.showToast({
					title: '打印失败: ' + error.message,
					icon: 'none'
				})
			}
		},

		/**
		 * 初始化打印机列表
		 */
		async initPrinters() {
			try {
				const printers = await PrintUtils.detectPrinters()
				this.printerOptions = printers
			} catch (error) {
				// 初始化打印机失败
			}
		},

		/**
		 * 执行打印
		 */
		async executePrint(images, settings) {
			// 使用PrintUtils执行打印
			await PrintUtils.executePrint(images, settings)
		},




		
		/**
		 * 格式化日期
		 */
		formatDate(dateString) {
			if (!dateString) return ''
			
			const date = new Date(dateString)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			
			return `${year}-${month}-${day}`
		},
		
		/**
		 * 格式化日期时间
		 */
		formatDateTime(dateString) {
			if (!dateString) return ''
			
			const date = new Date(dateString)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			
			return `${year}-${month}-${day} ${hours}:${minutes}`
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;

	// PC端适配
	@media (min-width: 768px) {
		padding: 15px;
		max-width: 1400px;
		margin: 0 auto;
	}
}

.search-section {
	padding: 15px;
	margin-bottom: 15px;

	@media (min-width: 768px) {
		padding: 0;
		margin-bottom: 20px;
	}
}

.search-card {
	background: #ffffff;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

	@media (min-width: 768px) {
		border-radius: 16px;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	}
}

.search-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 18px;
	background: linear-gradient(135deg, #007AFF, #4DA6FF);
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		background: linear-gradient(135deg, #0056CC, #007AFF);
	}

	@media (min-width: 768px) {
		padding: 22px;
	}
}

.title-text {
	font-size: 16px;
	font-weight: 700;
	color: #ffffff;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}

.toggle-btn {
	display: flex;
	align-items: center;
	color: #ffffff;
	font-size: 12px;
	cursor: pointer;
	transition: transform 0.3s ease;

	&:hover {
		transform: scale(1.03);
	}

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.toggle-icon {
	margin-left: 6px;
	font-size: 10px;
	transition: transform 0.3s ease;

	@media (min-width: 768px) {
		margin-left: 8px;
		font-size: 12px;
	}
}

.search-form {
	padding: 18px;

	@media (min-width: 768px) {
		padding: 22px;
		display: flex;
		flex-direction: column;
		gap: 18px;
	}

	@media (min-width: 1200px) {
		gap: 20px;
	}
}

.search-filters-row {
	display: flex;
	flex-direction: column;
	gap: 18px;
	margin-bottom: 20px;

	@media (min-width: 768px) {
		flex-direction: row;
		gap: 15px;
		align-items: flex-end;
	}

	@media (min-width: 1200px) {
		gap: 20px;
	}

	&:last-of-type {
		margin-bottom: 0;
	}
}

.search-filters-row .form-row {
	margin-bottom: 0;
	flex: 1;
	min-width: 0; /* 防止flex子元素溢出 */

	@media (min-width: 768px) {
		min-width: 200px; /* 确保每个字段有最小宽度 */
	}
}

.form-row {
	margin-bottom: 25px;

	@media (min-width: 768px) {
		margin-bottom: 0;
	}
}

.form-label {
	display: block;
	font-size: 16px;
	color: #333;
	margin-bottom: 12px;
	font-weight: 600;

	@media (min-width: 768px) {
		font-size: 18px;
		margin-bottom: 15px;
	}
}

.form-input {
	width: 100%;
	height: 50px;
	padding: 0 16px;
	border: 2px solid #e8e8e8;
	border-radius: 12px;
	font-size: 16px;
	color: #333;
	background: #ffffff;
	transition: all 0.3s ease;
	box-sizing: border-box; /* 确保padding和border包含在width内 */

	&:focus {
		border-color: #007AFF;
		box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
		outline: none;
	}

	&::placeholder {
		color: #bbb;
	}

	@media (min-width: 768px) {
		height: 56px;
		padding: 0 20px;
		border-radius: 14px;
		font-size: 18px;
	}
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 50px;
	padding: 0 16px;
	border: 2px solid #e8e8e8;
	border-radius: 12px;
	background: #ffffff;
	cursor: pointer;
	transition: all 0.3s ease;
	box-sizing: border-box; /* 确保padding和border包含在width内 */

	&:hover {
		border-color: #007AFF;
	}

	@media (min-width: 768px) {
		height: 56px;
		padding: 0 20px;
		border-radius: 14px;
	}
}

.picker-text {
	font-size: 16px;
	color: #333;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}

.picker-arrow {
	font-size: 14px;
	color: #999;
	transition: transform 0.3s ease;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.date-range {
	display: flex;
	align-items: center;
	gap: 10px;

	@media (min-width: 768px) {
		gap: 12px;
	}

	@media (min-width: 1200px) {
		gap: 15px;
	}
}

.date-input {
	flex: 1;
	height: 50px;
	display: flex;
	align-items: center;
	padding: 0 12px;
	border: 2px solid #e8e8e8;
	border-radius: 12px;
	background: #ffffff;
	cursor: pointer;
	transition: all 0.3s ease;
	box-sizing: border-box; /* 确保padding和border包含在width内 */
	min-width: 100px; /* 确保最小宽度 */

	&:hover {
		border-color: #007AFF;
	}

	@media (min-width: 768px) {
		height: 56px;
		padding: 0 16px;
		border-radius: 14px;
		min-width: 120px;
	}
}

.date-text {
	font-size: 16px;
	color: #333;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}

.date-separator {
	font-size: 12px;
	color: #666;
	font-weight: 500;
	white-space: nowrap; /* 防止换行 */

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.search-actions {
	display: flex;
	justify-content: center;
	gap: 15px;
	margin-top: 30px;

	@media (min-width: 768px) {
		gap: 20px;
		margin-top: 20px;
		justify-content: center;
	}
}

.action-btn {
	width: 120px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12px;
	font-size: 16px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	border: none;

	&:hover {
		transform: translateY(-2px);
	}

	@media (min-width: 768px) {
		width: 140px;
		height: 56px;
		border-radius: 14px;
		font-size: 18px;
	}
}

.reset-btn {
	background: #f5f5f5;
	color: #666;

	&:hover {
		background: #e9ecef;
		color: #333;
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
	}
}

.search-btn {
	background: linear-gradient(135deg, #007AFF, #0056CC);
	color: #ffffff;
	box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);

	&:hover {
		box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
	}
}

.stats-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20px 20px;

	@media (min-width: 768px) {
		padding: 0 0 30px;
	}
}

.stats-info {
	display: flex;
	flex-direction: column;
	gap: 4px;

	@media (min-width: 768px) {
		gap: 6px;
	}
}

.stats-text {
	font-size: 14px;
	color: #333;
	font-weight: 600;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.page-stats {
	font-size: 12px;
	color: #666;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.view-mode {
	display: flex;
	gap: 8px;
	background: #f5f5f5;
	padding: 4px;
	border-radius: 12px;

	@media (min-width: 768px) {
		gap: 10px;
		padding: 6px;
		border-radius: 14px;
	}
}

.mode-btn {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
	background: transparent;
	color: #666;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		background: #e9ecef;
		color: #333;
	}

	@media (min-width: 768px) {
		width: 50px;
		height: 50px;
		border-radius: 10px;
		font-size: 18px;
	}
}

.mode-btn.active {
	background: #007AFF;
	color: #ffffff;
	box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.image-list {
	padding: 0 20px;

	@media (min-width: 768px) {
		padding: 0;
	}
}

.image-list.grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20px;

	@media (min-width: 768px) {
		grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
		gap: 25px;
	}

	@media (min-width: 1200px) {
		grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
		gap: 30px;
	}
}

.image-list.list {
	display: flex;
	flex-direction: column;
	gap: 20px;

	@media (min-width: 768px) {
		gap: 25px;
	}
}

.image-list.list .image-item {
	display: flex;
	align-items: stretch;
}

.image-item {
	background: #ffffff;
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	cursor: pointer;

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);

		.image-overlay {
			opacity: 1;
		}
	}

	@media (min-width: 768px) {
		border-radius: 20px;
	}
}

.image-wrapper {
	position: relative;
	width: 100%;
	height: 200px;
	overflow: hidden;
	background: #f5f5f5;

	@media (min-width: 768px) {
		height: 240px;
	}
}

.image-list.list .image-wrapper {
	width: 200px;
	height: 150px;
	flex-shrink: 0;

	@media (min-width: 768px) {
		width: 250px;
		height: 180px;
	}
}

.image-thumb {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: all 0.3s ease;
	background: #f5f5f5;
}

.image-item:hover .image-thumb {
	transform: scale(1.05);
}

/* 分页样式 */
.pagination {
	margin: 40px 20px 20px;
	background: #ffffff;
	border-radius: 16px;
	padding: 20px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

	@media (min-width: 768px) {
		margin: 50px 30px 30px;
		padding: 25px;
		border-radius: 20px;
	}
}

.pagination-info {
	text-align: center;
	margin-bottom: 20px;

	@media (min-width: 768px) {
		margin-bottom: 25px;
	}
}

.page-info {
	color: #666;
	font-size: 14px;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.pagination-controls {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	gap: 8px;

	@media (min-width: 768px) {
		gap: 10px;
	}
}

.page-btn {
	padding: 8px 12px;
	background: #f8f9fa;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 60px;
	text-align: center;

	&:hover:not(.disabled) {
		background: #007aff;
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);

		.btn-text {
			color: #ffffff;
		}
	}

	&.disabled {
		opacity: 0.5;
		cursor: not-allowed;
		pointer-events: none;
	}

	@media (min-width: 768px) {
		padding: 10px 16px;
		border-radius: 10px;
		min-width: 70px;
	}
}

.btn-text {
	color: #333;
	font-size: 12px;
	font-weight: 500;
	transition: color 0.3s ease;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.page-numbers {
	display: flex;
	align-items: center;
	gap: 4px;
	margin: 0 8px;

	@media (min-width: 768px) {
		gap: 6px;
		margin: 0 12px;
	}
}

.page-number {
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	background: #f8f9fa;

	&:hover:not(.ellipsis) {
		background: #007aff;
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);

		.number-text {
			color: #ffffff;
		}
	}

	&.active {
		background: #007aff;
		box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);

		.number-text {
			color: #ffffff;
			font-weight: bold;
		}
	}

	&.ellipsis {
		cursor: default;
		background: transparent;

		&:hover {
			background: transparent;
			transform: none;
			box-shadow: none;
		}
	}

	@media (min-width: 768px) {
		width: 40px;
		height: 40px;
		border-radius: 10px;
	}
}

.number-text {
	color: #333;
	font-size: 12px;
	font-weight: 500;
	transition: color 0.3s ease;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

/* 加载状态样式 */
.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
	margin: 20px;
	background: #ffffff;
	border-radius: 16px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

	@media (min-width: 768px) {
		padding: 50px 30px;
		margin: 30px;
		border-radius: 20px;
	}
}

.loading-spinner {
	width: 40px;
	height: 40px;
	border: 4px solid #f3f3f3;
	border-top: 4px solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 15px;

	@media (min-width: 768px) {
		width: 50px;
		height: 50px;
		border-width: 5px;
		margin-bottom: 20px;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	color: #666;
	font-size: 14px;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

/* 快速跳转样式 */
.quick-jump {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	margin-top: 20px;
	padding-top: 20px;
	border-top: 1px solid #eee;

	@media (min-width: 768px) {
		gap: 12px;
		margin-top: 25px;
		padding-top: 25px;
	}
}

.jump-label {
	color: #666;
	font-size: 14px;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.jump-input {
	width: 60px;
	height: 32px;
	border: 1px solid #ddd;
	border-radius: 6px;
	text-align: center;
	font-size: 14px;
	background: #fff;

	&:focus {
		border-color: #007aff;
		outline: none;
	}

	@media (min-width: 768px) {
		width: 70px;
		height: 36px;
		border-radius: 8px;
		font-size: 16px;
	}
}

.jump-btn {
	padding: 6px 12px;
	background: #007aff;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		background: #0056cc;
		transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
	}

	@media (min-width: 768px) {
		padding: 8px 16px;
		border-radius: 8px;
	}
}

.jump-text {
	color: #ffffff;
	font-size: 12px;
	font-weight: 500;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}



.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, rgba(0, 122, 255, 0.8), rgba(0, 86, 204, 0.8));
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: all 0.3s ease;
}

.overlay-icon {
	font-size: 30px;
	color: #ffffff;

	@media (min-width: 768px) {
		font-size: 40px;
	}
}

.image-info {
	padding: 20px;

	@media (min-width: 768px) {
		padding: 25px;
	}
}

.image-list.list .image-info {
	flex: 1;
	padding: 20px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	@media (min-width: 768px) {
		padding: 25px;
	}
}

.image-name {
	font-size: 16px;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 12px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	@media (min-width: 768px) {
		font-size: 18px;
		margin-bottom: 15px;
	}
}

.image-factory,
.image-order,
.image-size,
.image-date {
	font-size: 14px;
	color: #666;
	display: block;
	margin-bottom: 6px;

	@media (min-width: 768px) {
		font-size: 16px;
		margin-bottom: 8px;
	}
}



.empty-state {
	text-align: center;
	padding: 80px 20px;

	@media (min-width: 768px) {
		padding: 120px 30px;
	}
}

.empty-icon {
	font-size: 80px;
	display: block;
	margin-bottom: 20px;

	@media (min-width: 768px) {
		font-size: 120px;
		margin-bottom: 30px;
	}
}

.empty-text {
	font-size: 20px;
	color: #666;
	display: block;
	margin-bottom: 12px;
	font-weight: 600;

	@media (min-width: 768px) {
		font-size: 24px;
		margin-bottom: 15px;
	}
}

.empty-desc {
	font-size: 16px;
	color: #999;
	display: block;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}

.preview-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.9);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	backdrop-filter: blur(10px);

	@media (min-width: 768px) {
		padding: 40px;
	}
}

.preview-content {
	width: 100%;
	max-width: 900px;
	max-height: 100%;
	background: #ffffff;
	border-radius: 20px;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);

	@media (min-width: 768px) {
		border-radius: 24px;
	}
}

.preview-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	background: #f8f9fa;
	border-bottom: 1px solid #e8e8e8;

	@media (min-width: 768px) {
		padding: 25px;
	}
}

.preview-controls {
	display: flex;
	gap: 8px;
	margin: 0 15px;

	@media (min-width: 768px) {
		gap: 10px;
		margin: 0 20px;
	}
}

.control-btn {
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
	background: #ffffff;
	border: 1px solid #e8e8e8;
	color: #666;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 16px;
	font-weight: bold;

	&:hover {
		background: #007AFF;
		color: #ffffff;
		border-color: #007AFF;
		transform: scale(1.05);
	}

	&:active {
		transform: scale(0.95);
	}

	@media (min-width: 768px) {
		width: 40px;
		height: 40px;
		border-radius: 10px;
		font-size: 18px;
	}
}

.control-icon {
	line-height: 1;
}

.preview-title {
	font-size: 18px;
	font-weight: 700;
	color: #333;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: 15px;

	@media (min-width: 768px) {
		font-size: 20px;
		margin-right: 20px;
	}
}

.close-btn {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: #f5f5f5;
	color: #666;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		background: #e9ecef;
		color: #333;
		transform: scale(1.1);
	}

	@media (min-width: 768px) {
		width: 50px;
		height: 50px;
	}
}

.close-icon {
	font-size: 16px;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}

.preview-image-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	min-height: 300px;
	background: #f8f9fa;
	overflow: hidden;
	cursor: pointer;
	user-select: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;

	@media (min-width: 768px) {
		padding: 30px;
		min-height: 400px;
	}
}

.image-container {
	display: flex;
	align-items: center;
	justify-content: center;
	transform-origin: center center;
	will-change: transform;
}

.preview-image {
	max-width: 100%;
	max-height: 100%;
	border-radius: 12px;
	box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
	user-select: none;
	pointer-events: none;
}

.preview-info {
	padding: 20px;
	background: #ffffff;
	border-top: 1px solid #e8e8e8;

	@media (min-width: 768px) {
		padding: 25px;
	}
}

.info-row {
	display: flex;
	gap: 20px;
	margin-bottom: 12px;

	@media (min-width: 768px) {
		gap: 30px;
		margin-bottom: 15px;
	}
}

.info-item {
	display: flex;
	flex: 1;
	font-size: 14px;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.info-label {
	color: #666;
	width: 80px;
	flex-shrink: 0;
	font-weight: 600;

	@media (min-width: 768px) {
		width: 90px;
	}
}

.info-value {
	color: #333;
	flex: 1;
	font-weight: 400;
}

/* 图片选择样式 */
.image-item {
	position: relative;

	&.selected {
		.image-wrapper {
			border: 3px solid #007aff;
			border-radius: 12px;
		}
	}
}

.image-checkbox {
	position: absolute;
	top: 8px;
	left: 8px;
	z-index: 10;
	cursor: pointer;

	@media (min-width: 768px) {
		top: 12px;
		left: 12px;
	}
}

.checkbox {
	width: 20px;
	height: 20px;
	border: 2px solid #ddd;
	border-radius: 4px;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.checked {
		background: #007aff;
		border-color: #007aff;
	}

	&:hover {
		border-color: #007aff;
		transform: scale(1.1);
	}

	@media (min-width: 768px) {
		width: 24px;
		height: 24px;
		border-radius: 6px;
	}
}

.check-icon {
	color: #ffffff;
	font-size: 12px;
	font-weight: bold;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

/* 批量操作样式 */
.action-buttons {
	display: flex;
	align-items: center;
	gap: 15px;
	flex-wrap: wrap;

	@media (min-width: 768px) {
		gap: 20px;
	}
}

.batch-actions {
	display: flex;
	align-items: center;
	gap: 10px;
	flex-wrap: wrap;

	@media (min-width: 768px) {
		gap: 15px;
	}
}

.selected-count {
	color: #007aff;
	font-size: 12px;
	font-weight: 600;
	white-space: nowrap;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 6px 12px;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 12px;
	white-space: nowrap;

	&.print-btn {
		background: #007aff;
		color: #ffffff;

		&:hover {
			background: #0056cc;
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
		}
	}

	&.clear-btn {
		background: #f8f9fa;
		color: #666;

		&:hover {
			background: #e9ecef;
			color: #333;
		}
	}

	@media (min-width: 768px) {
		gap: 6px;
		padding: 8px 16px;
		border-radius: 10px;
		font-size: 14px;
	}
}

.btn-icon {
	font-size: 14px;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.btn-text {
	font-weight: 500;
}

/* 打印按钮样式 */
.print-single-btn {
	background: #28a745 !important;

	&:hover {
		background: #218838 !important;
		box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
	}
}

/* 打印模态框样式 */
.print-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	padding: 20px;
}

.print-content {
	background: #ffffff;
	border-radius: 16px;
	width: 100%;
	max-width: 800px;
	max-height: 90vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);

	@media (min-width: 768px) {
		border-radius: 20px;
		max-width: 900px;
	}
}

.print-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 25px;
	border-bottom: 1px solid #eee;
	background: #f8f9fa;

	@media (min-width: 768px) {
		padding: 25px 30px;
	}
}

.print-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;

	@media (min-width: 768px) {
		font-size: 20px;
	}
}

.print-body {
	flex: 1;
	overflow-y: auto;
	padding: 25px;

	@media (min-width: 768px) {
		padding: 30px;
	}
}

.print-preview {
	margin-bottom: 30px;

	@media (min-width: 768px) {
		margin-bottom: 35px;
	}
}

.preview-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 15px;
	display: block;

	@media (min-width: 768px) {
		font-size: 18px;
		margin-bottom: 20px;
	}
}

.preview-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
	gap: 15px;

	@media (min-width: 768px) {
		grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
		gap: 20px;
	}
}

.preview-item {
	text-align: center;
	padding: 10px;
	border: 1px solid #eee;
	border-radius: 8px;
	background: #f8f9fa;

	@media (min-width: 768px) {
		padding: 12px;
		border-radius: 10px;
	}
}

.preview-thumb {
	width: 100%;
	height: 80px;
	object-fit: cover;
	border-radius: 6px;
	margin-bottom: 8px;

	@media (min-width: 768px) {
		height: 100px;
		border-radius: 8px;
		margin-bottom: 10px;
	}
}

.preview-name {
	font-size: 11px;
	color: #666;
	word-break: break-all;
	line-height: 1.3;

	@media (min-width: 768px) {
		font-size: 12px;
	}
}

.preview-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	border: 2px dashed #ddd;
	border-radius: 8px;
	background: #f8f9fa;

	@media (min-width: 768px) {
		padding: 25px;
		border-radius: 10px;
	}
}

.more-text {
	color: #666;
	font-size: 14px;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.print-settings {
	display: grid;
	gap: 20px;

	@media (min-width: 768px) {
		gap: 25px;
		grid-template-columns: repeat(2, 1fr);
	}
}

.setting-group {
	display: flex;
	flex-direction: column;
	gap: 8px;

	@media (min-width: 768px) {
		gap: 10px;
	}
}

.setting-label {
	font-size: 14px;
	font-weight: 600;
	color: #333;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 15px;
	border: 1px solid #ddd;
	border-radius: 8px;
	background: #ffffff;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		border-color: #007aff;
	}

	@media (min-width: 768px) {
		padding: 14px 18px;
		border-radius: 10px;
	}
}

.picker-text {
	font-size: 14px;
	color: #333;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.picker-arrow {
	font-size: 12px;
	color: #666;
	transition: transform 0.3s ease;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.print-footer {
	display: flex;
	gap: 15px;
	padding: 20px 25px;
	border-top: 1px solid #eee;
	background: #f8f9fa;

	@media (min-width: 768px) {
		gap: 20px;
		padding: 25px 30px;
	}
}

/* 骨架屏样式 */
.skeleton-container {
	width: 100%;
}

.skeleton-item {
	margin-bottom: 20rpx;
}

.image-list.grid .skeleton-item {
	width: calc(50% - 10rpx);
	margin-right: 20rpx;
}

.image-list.grid .skeleton-item:nth-child(2n) {
	margin-right: 0;
}

.footer-btn {
	flex: 1;
	padding: 12px 20px;
	border-radius: 8px;
	text-align: center;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 14px;
	font-weight: 600;

	&.cancel-btn {
		background: #f8f9fa;
		color: #666;
		border: 1px solid #ddd;

		&:hover {
			background: #e9ecef;
			color: #333;
		}
	}

	&.confirm-btn {
		background: #007aff;
		color: #ffffff;

		&:hover {
			background: #0056cc;
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
		}
	}

	@media (min-width: 768px) {
		padding: 14px 25px;
		border-radius: 10px;
		font-size: 16px;
	}
}


</style>
