package com.finance.controller;

import com.finance.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统健康检查控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/health")
public class HealthController {

    @Autowired
    private DataSource dataSource;

    /**
     * 系统健康检查
     */
    @GetMapping("/check")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查系统时间
            health.put("timestamp", LocalDateTime.now());
            health.put("status", "UP");
            
            // 检查数据库连接
            boolean dbHealthy = checkDatabase();
            health.put("database", dbHealthy ? "UP" : "DOWN");
            
            // 检查内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Object> memory = new HashMap<>();
            memory.put("total", formatBytes(totalMemory));
            memory.put("used", formatBytes(usedMemory));
            memory.put("free", formatBytes(freeMemory));
            memory.put("usage", String.format("%.2f%%", (double) usedMemory / totalMemory * 100));
            health.put("memory", memory);
            
            // 检查磁盘空间
            Map<String, Object> disk = new HashMap<>();
            java.io.File root = new java.io.File("/");
            disk.put("total", formatBytes(root.getTotalSpace()));
            disk.put("free", formatBytes(root.getFreeSpace()));
            disk.put("used", formatBytes(root.getTotalSpace() - root.getFreeSpace()));
            health.put("disk", disk);
            
            return Result.success("系统健康检查完成", health);
            
        } catch (Exception e) {
            log.error("健康检查失败: ", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            return Result.error("系统健康检查失败");
        }
    }
    
    /**
     * 检查数据库连接
     */
    private boolean checkDatabase() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (Exception e) {
            log.error("数据库连接检查失败: ", e);
            return false;
        }
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.2f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
