import{s as e,c as a,w as l,i as t,o as s,a as c,b as d,d as u}from"./index-BNdZE9oc.js";import{_ as o}from"./_plugin-vue_export-helper.BCo6x5W8.js";const f=o({data:()=>({}),onLoad(){},methods:{navigateToFactory(){e({url:"/pages/factory/list"})},navigateToImageSearch(){e({url:"/pages/image/search"})}}},[["render",function(e,o,f,_,i,m){const n=u,r=t;return s(),a(r,{class:"container"},{default:l((()=>[c(r,{class:"header-section"},{default:l((()=>[c(r,{class:"welcome-card"},{default:l((()=>[c(r,{class:"welcome-content"},{default:l((()=>[c(n,{class:"welcome-title"},{default:l((()=>[d("订单图片管理系统")])),_:1}),c(n,{class:"welcome-subtitle"},{default:l((()=>[d("Order Image Management System")])),_:1}),c(n,{class:"welcome-desc"},{default:l((()=>[d("高效管理订单图片，轻松检索文件信息")])),_:1})])),_:1}),c(r,{class:"welcome-icon"},{default:l((()=>[c(n,{class:"icon-text"},{default:l((()=>[d("📋")])),_:1})])),_:1})])),_:1})])),_:1}),c(r,{class:"modules-section"},{default:l((()=>[c(r,{class:"section-title"},{default:l((()=>[c(n,{class:"title-text"},{default:l((()=>[d("功能模块")])),_:1}),c(n,{class:"title-desc"},{default:l((()=>[d("选择您需要的功能模块")])),_:1})])),_:1}),c(r,{class:"modules-grid"},{default:l((()=>[c(r,{class:"module-item",onClick:m.navigateToFactory},{default:l((()=>[c(r,{class:"module-header"},{default:l((()=>[c(r,{class:"module-icon factory-icon"},{default:l((()=>[c(n,{class:"icon-text"},{default:l((()=>[d("🏭")])),_:1})])),_:1}),c(r,{class:"module-badge"},{default:l((()=>[d("管理")])),_:1})])),_:1}),c(r,{class:"module-content"},{default:l((()=>[c(n,{class:"module-title"},{default:l((()=>[d("工厂管理")])),_:1}),c(n,{class:"module-desc"},{default:l((()=>[d("管理工厂信息，维护基础数据")])),_:1}),c(r,{class:"module-features"},{default:l((()=>[c(n,{class:"feature-item"},{default:l((()=>[d("• 新增工厂")])),_:1}),c(n,{class:"feature-item"},{default:l((()=>[d("• 编辑信息")])),_:1}),c(n,{class:"feature-item"},{default:l((()=>[d("• 状态管理")])),_:1})])),_:1})])),_:1}),c(r,{class:"module-footer"},{default:l((()=>[c(n,{class:"action-text"},{default:l((()=>[d("立即管理 →")])),_:1})])),_:1})])),_:1},8,["onClick"]),c(r,{class:"module-item",onClick:m.navigateToImageSearch},{default:l((()=>[c(r,{class:"module-header"},{default:l((()=>[c(r,{class:"module-icon search-icon"},{default:l((()=>[c(n,{class:"icon-text"},{default:l((()=>[d("🔍")])),_:1})])),_:1}),c(r,{class:"module-badge"},{default:l((()=>[d("检索")])),_:1})])),_:1}),c(r,{class:"module-content"},{default:l((()=>[c(n,{class:"module-title"},{default:l((()=>[d("图片检索")])),_:1}),c(n,{class:"module-desc"},{default:l((()=>[d("快速查找订单图片，支持多条件搜索")])),_:1}),c(r,{class:"module-features"},{default:l((()=>[c(n,{class:"feature-item"},{default:l((()=>[d("• 智能搜索")])),_:1}),c(n,{class:"feature-item"},{default:l((()=>[d("• 图片预览")])),_:1}),c(n,{class:"feature-item"},{default:l((()=>[d("• 批量操作")])),_:1})])),_:1})])),_:1}),c(r,{class:"module-footer"},{default:l((()=>[c(n,{class:"action-text"},{default:l((()=>[d("开始检索 →")])),_:1})])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-cd9ae7ee"]]);export{f as default};
