<template>
  <view class="skeleton-loader" :class="{ animated: animated }">
    <!-- 图片骨架屏 -->
    <view v-if="type === 'image'" class="skeleton-image" :style="imageStyle"></view>
    
    <!-- 文本骨架屏 -->
    <view v-else-if="type === 'text'" class="skeleton-text" :style="textStyle"></view>
    
    <!-- 卡片骨架屏 -->
    <view v-else-if="type === 'card'" class="skeleton-card">
      <view class="skeleton-image" :style="{ width: '100%', height: '200rpx' }"></view>
      <view class="skeleton-content">
        <view class="skeleton-text" :style="{ width: '80%', height: '32rpx' }"></view>
        <view class="skeleton-text" :style="{ width: '60%', height: '28rpx', marginTop: '16rpx' }"></view>
      </view>
    </view>
    
    <!-- 列表项骨架屏 -->
    <view v-else-if="type === 'list-item'" class="skeleton-list-item">
      <view class="skeleton-avatar"></view>
      <view class="skeleton-content">
        <view class="skeleton-text" :style="{ width: '70%', height: '32rpx' }"></view>
        <view class="skeleton-text" :style="{ width: '50%', height: '28rpx', marginTop: '12rpx' }"></view>
      </view>
    </view>
    
    <!-- 自定义骨架屏 -->
    <view v-else class="skeleton-custom">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SkeletonLoader',
  props: {
    // 骨架屏类型
    type: {
      type: String,
      default: 'text',
      validator: value => ['image', 'text', 'card', 'list-item', 'custom'].includes(value)
    },
    // 是否显示动画
    animated: {
      type: Boolean,
      default: true
    },
    // 宽度
    width: {
      type: [String, Number],
      default: '100%'
    },
    // 高度
    height: {
      type: [String, Number],
      default: '32rpx'
    },
    // 圆角
    borderRadius: {
      type: [String, Number],
      default: '8rpx'
    }
  },
  
  computed: {
    imageStyle() {
      return {
        width: typeof this.width === 'number' ? this.width + 'rpx' : this.width,
        height: typeof this.height === 'number' ? this.height + 'rpx' : this.height,
        borderRadius: typeof this.borderRadius === 'number' ? this.borderRadius + 'rpx' : this.borderRadius
      }
    },
    
    textStyle() {
      return {
        width: typeof this.width === 'number' ? this.width + 'rpx' : this.width,
        height: typeof this.height === 'number' ? this.height + 'rpx' : this.height,
        borderRadius: typeof this.borderRadius === 'number' ? this.borderRadius + 'rpx' : this.borderRadius
      }
    }
  }
}
</script>

<style scoped>
.skeleton-loader {
  display: flex;
  flex-direction: column;
}

.skeleton-image,
.skeleton-text,
.skeleton-avatar {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
}

.skeleton-loader.animated .skeleton-image,
.skeleton-loader.animated .skeleton-text,
.skeleton-loader.animated .skeleton-avatar {
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
}

.skeleton-text {
  height: 32rpx;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
}

.skeleton-text:last-child {
  margin-bottom: 0;
}

.skeleton-card {
  border-radius: 12rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.skeleton-card .skeleton-content {
  padding: 24rpx;
}

.skeleton-list-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.skeleton-list-item .skeleton-content {
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .skeleton-card .skeleton-content {
    padding: 20rpx;
  }
  
  .skeleton-list-item {
    padding: 20rpx;
  }
  
  .skeleton-avatar {
    width: 60rpx;
    height: 60rpx;
    margin-right: 20rpx;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .skeleton-image,
  .skeleton-text,
  .skeleton-avatar {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
  
  .skeleton-card {
    background: #1a1a1a;
    box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
  }
  
  .skeleton-list-item {
    background: #1a1a1a;
  }
}
</style>
