package com.finance.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.net.URLDecoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;

/**
 * 图片文件访问控制器
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/images")
public class ImageFileController {

    @Value("${finance.image.storage-path:UploadImg/}")
    private String storagePath;

    @Value("${finance.image.cache.max-age:3600}")
    private long cacheMaxAge;

    /**
     * 获取图片文件
     */
    @GetMapping("/**")
    public ResponseEntity<Resource> getImage(@RequestParam(required = false) String path,
                                           @RequestParam(required = false) String size,
                                           @RequestParam(required = false) String format,
                                           HttpServletRequest request) {
        try {
            // 从请求URI中提取文件路径
            String requestURI = request.getRequestURI();
            String filePath = requestURI.substring("/api/images/".length());

            if (path != null && !path.isEmpty()) {
                filePath = path;
            }

            log.info("请求图片文件: {}", filePath);

            // URL解码
            filePath = URLDecoder.decode(filePath, "UTF-8");

            // 判断是否为绝对路径
            Path fullPath;
            if (isAbsolutePath(filePath)) {
                // 如果是绝对路径，直接使用
                fullPath = Paths.get(filePath).normalize();
            } else {
                // 如果是相对路径，与storagePath拼接
                fullPath = Paths.get(storagePath, filePath).normalize();
            }

            File file = fullPath.toFile();

            if (!file.exists() || !file.isFile()) {
                log.warn("图片文件不存在: {}", fullPath);
                return ResponseEntity.notFound().build();
            }

            // 安全检查：确保文件存在且可读
            if (!file.canRead()) {
                log.warn("图片文件无法读取: {}", fullPath);
                return ResponseEntity.badRequest().build();
            }

            Resource resource = new FileSystemResource(file);

            // 根据文件扩展名设置Content-Type
            String contentType = getContentType(filePath, format);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .cacheControl(CacheControl.maxAge(Duration.ofSeconds(cacheMaxAge)))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"")
                    .header("X-Image-Size", size != null ? size : "original")
                    .body(resource);

        } catch (Exception e) {
            log.error("获取图片文件失败: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 判断是否为绝对路径
     */
    private boolean isAbsolutePath(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }

        // Windows绝对路径：C:\ 或 D:\ 等
        if (path.length() >= 3 && path.charAt(1) == ':' && (path.charAt(2) == '\\' || path.charAt(2) == '/')) {
            return true;
        }

        // Unix/Linux绝对路径：以 / 开头
        if (path.startsWith("/")) {
            return true;
        }

        return false;
    }
    
    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String filePath, String format) {
        // 如果指定了格式，优先使用指定格式
        if (format != null && !format.isEmpty()) {
            switch (format.toLowerCase()) {
                case "webp":
                    return "image/webp";
                case "jpeg":
                case "jpg":
                    return "image/jpeg";
                case "png":
                    return "image/png";
                case "gif":
                    return "image/gif";
                case "bmp":
                    return "image/bmp";
            }
        }

        // 否则根据文件扩展名判断
        String extension = "";
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filePath.substring(lastDotIndex + 1).toLowerCase();
        }

        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            default:
                return "application/octet-stream";
        }
    }
}
