package com.finance.controller;

import com.finance.common.PageResult;
import com.finance.common.Result;
import com.finance.dto.ImageInfoDTO;
import com.finance.dto.ImageQueryDTO;
import com.finance.service.ImageInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 图片信息控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/image")
public class ImageInfoController {

    @Autowired
    private ImageInfoService imageInfoService;

    /**
     * 分页查询图片信息
     */
    @GetMapping("/page")
    public Result<PageResult<ImageInfoDTO>> getImagePage(ImageQueryDTO queryDTO) {
        PageResult<ImageInfoDTO> pageResult = imageInfoService.getImagePage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID获取图片信息
     */
    @GetMapping("/{id}")
    public Result<ImageInfoDTO> getImageById(@PathVariable Integer id) {
        ImageInfoDTO imageInfo = imageInfoService.getImageById(id);
        return Result.success(imageInfo);
    }

    /**
     * 获取所有工厂名称列表
     */
    @GetMapping("/factories")
    public Result<List<String>> getFactoryNames() {
        log.info("获取所有工厂名称列表");
        List<String> factoryNames = imageInfoService.getFactoryNames();
        return Result.success(factoryNames);
    }

    /**
     * 根据工厂名称获取订单号列表
     */
    @GetMapping("/orders")
    public Result<List<String>> getOrderNumbersByFactory(@RequestParam String factoryName) {
        log.info("根据工厂名称获取订单号列表，工厂名称: {}", factoryName);
        List<String> orderNumbers = imageInfoService.getOrderNumbersByFactory(factoryName);
        return Result.success(orderNumbers);
    }

    /**
     * 根据工厂名称统计图片数量
     */
    @GetMapping("/count/factory")
    public Result<Long> countByFactory(@RequestParam String factoryName) {
        log.info("根据工厂名称统计图片数量，工厂名称: {}", factoryName);
        long count = imageInfoService.countByFactory(factoryName);
        return Result.success(count);
    }

    /**
     * 根据订单号统计图片数量
     */
    @GetMapping("/count/order")
    public Result<Long> countByOrderNumber(@RequestParam String orderNumber) {
        log.info("根据订单号统计图片数量，订单号: {}", orderNumber);
        long count = imageInfoService.countByOrderNumber(orderNumber);
        return Result.success(count);
    }
}
