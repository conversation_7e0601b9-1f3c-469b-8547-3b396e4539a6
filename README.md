# 订单图片管理系统 (Order Image Management System)

一个基于Spring Boot + uni-app的完整Web应用系统，提供工厂管理和图片检索功能，专为订单图片管理而设计。

## 技术栈

### 后端
- **框架**: Spring Boot 2.7.18
- **Java版本**: Java 8 (1.8.0_181)
- **数据库**: MySQL 8.0
- **ORM**: Spring Data JPA + Hibernate
- **安全**: Spring Security (密码加密)
- **构建工具**: Maven

### 前端
- **框架**: uni-app (Vue 3)
- **编译目标**: Web端
- **UI组件**: 自定义组件
- **HTTP客户端**: uni.request

## 项目结构

```
Finance_Web/
├── Front/                  # uni-app前端项目
│   ├── api/               # API接口封装
│   ├── config/            # 配置文件
│   ├── pages/             # 页面文件
│   │   ├── index/         # 首页
│   │   ├── factory/       # 工厂管理
│   │   └── image/         # 图片检索
│   ├── static/            # 静态资源
│   ├── utils/             # 工具函数
│   └── test.html          # API测试页面
├── Server/                # Spring Boot后端项目
│   ├── src/main/java/com/finance/
│   │   ├── common/        # 通用类
│   │   ├── config/        # 配置类
│   │   ├── controller/    # 控制器
│   │   ├── dto/           # 数据传输对象
│   │   ├── entity/        # 实体类
│   │   ├── repository/    # 数据访问层
│   │   ├── service/       # 业务逻辑层
│   │   └── util/          # 工具类
│   └── src/main/resources/
│       └── application.yml # 应用配置
└── README.md              # 项目说明
```

## 数据库配置

### 连接信息
- **主机**: 47.110.46.27:3306
- **用户名**: mls01
- **密码**: 12345@Mls
- **数据库**: identify

### 数据表
1. **Factory_Login** - 工厂登录表
2. **Img_Info** - 图片信息表

## 功能模块

### 1. 工厂管理系统
- ✅ 工厂信息的增删改查
- ✅ 用户名唯一性验证
- ✅ 密码BCrypt加密存储
- ✅ 状态管理（启用/禁用）
- ✅ 分页查询和条件搜索

### 2. 图片检索系统
- ✅ 多条件组合搜索
- ✅ 按工厂名称、订单号、时间范围检索
- ✅ 分页显示和加载更多
- ✅ 图片预览功能
- ✅ 网格/列表视图切换

## 快速开始

### 1. 启动后端服务

```bash
cd Server
mvn spring-boot:run
```

服务将在 http://localhost:8080 启动

### 2. 测试API接口

打开浏览器访问: `file:///d:/Desktop/Finance_Web/Front/test.html`

### 3. 开发前端应用

使用HBuilderX或其他uni-app开发工具打开Front目录，选择"运行到浏览器"。

## API接口文档

### 工厂管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/factory/page` | 分页查询工厂列表 |
| GET | `/api/factory/{id}` | 获取工厂详情 |
| POST | `/api/factory` | 创建工厂 |
| PUT | `/api/factory/{id}` | 更新工厂 |
| DELETE | `/api/factory/{id}` | 删除工厂 |
| GET | `/api/factory/active` | 获取启用工厂列表 |

### 图片检索API

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/image/page` | 分页查询图片列表 |
| GET | `/api/image/{id}` | 获取图片详情 |
| GET | `/api/image/factories` | 获取工厂名称列表 |
| GET | `/api/image/orders` | 获取订单号列表 |
| GET | `/api/image/count/factory` | 按工厂统计图片数量 |
| GET | `/api/image/count/order` | 按订单统计图片数量 |

## 开发说明

### 后端开发
- 使用Spring Boot 2.7 + Java 8
- 遵循RESTful API设计规范
- 统一的响应格式和异常处理
- 密码使用BCrypt加密
- 支持跨域请求

### 前端开发
- 基于uni-app Vue 3语法
- 响应式设计，适配不同屏幕
- 模块化API封装
- 统一的错误处理和加载提示
- 美观的UI设计

## 部署说明

### 后端部署
1. 打包应用: `mvn clean package`
2. 运行JAR文件: `java -jar target/finance-web-server-1.0.0.jar`

### 前端部署
1. 在HBuilderX中选择"发行" -> "网站-PC Web或手机H5"
2. 将生成的dist目录部署到Web服务器

## 注意事项

1. 确保MySQL数据库服务正常运行
2. 检查数据库连接配置是否正确
3. 前端开发时注意跨域问题
4. 图片文件路径需要正确配置
5. 生产环境需要修改数据库连接和API地址

## 联系方式

如有问题请联系开发团队。
