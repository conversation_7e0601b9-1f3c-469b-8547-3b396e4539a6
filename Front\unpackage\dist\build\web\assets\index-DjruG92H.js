function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-index-index.D_xV0tTS.js","assets/_plugin-vue_export-helper.BCo6x5W8.js","assets/index-Xal-2WHc.css","assets/pages-factory-list.CclG1KZ5.js","assets/factory.o0XEgZaa.js","assets/request.j3WTH_xy.js","assets/performance.o1AJnB7v.js","assets/list-BJlnZy5t.css","assets/pages-factory-form.CpmpgYiW.js","assets/form--_x2fuMD.css","assets/pages-image-search.DkwsEPrs.js","assets/search-A6zqzyr7.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){const t=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(n.map((n=>{if((n=function(e){return"/"+e}(n))in e)return;e[n]=!0;const r=n.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let e=t.length-1;e>=0;e--){const o=t[e];if(o.href===n&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=n,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,t)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${n}`))))})):void 0})))}return r.then((()=>t())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function n(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const o={},r=[],i=()=>{},s=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,f=(e,t)=>d.call(e,t),p=Array.isArray,h=e=>"[object Map]"===x(e),g=e=>"[object Set]"===x(e),m=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,b=e=>(_(e)||m(e))&&m(e.then)&&m(e.catch),w=Object.prototype.toString,x=e=>w.call(e),S=e=>"[object Object]"===x(e),C=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,T=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},k=/-(\w)/g,O=E((e=>e.replace(k,((e,t)=>t?t.toUpperCase():"")))),A=/\B([A-Z])/g,$=E((e=>e.replace(A,"-$1").toLowerCase())),L=E((e=>e.charAt(0).toUpperCase()+e.slice(1))),P=E((e=>e?`on${L(e)}`:"")),M=(e,t)=>!Object.is(e,t),R=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},B=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},I=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let j;const D=()=>j||(j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function N(e){if(p(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?W(o):N(o);if(r)for(const e in r)t[e]=r[e]}return t}if(v(e)||_(e))return e}const F=/;(?![^(]*\))/g,V=/:([^]+)/,H=/\/\*[^]*?\*\//g;function W(e){const t={};return e.replace(H,"").split(F).forEach((e=>{if(e){const n=e.split(V);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function z(e){let t="";if(v(e))t=e;else if(p(e))for(let n=0;n<e.length;n++){const o=z(e[n]);o&&(t+=o+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const q=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function U(e){return!!e||""===e}const Y=e=>v(e)?e:null==e?"":p(e)||_(e)&&(e.toString===w||!m(e.toString))?JSON.stringify(e,X,2):String(e),X=(e,t)=>t&&t.__v_isRef?X(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[K(t,o)+" =>"]=n,e)),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>K(e)))}:y(t)?K(t):!_(t)||p(t)||S(t)?t:String(t),K=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},J=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map((e=>"uni-"+e)),G=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),Q=["list-item"].map((e=>"uni-"+e));function Z(e){if(-1!==Q.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==J.indexOf(t)||-1!==G.indexOf(t)}const ee=["%","%"],te=/^([a-z-]+:)?\/\//i,ne=/^data:.*,.*/;function oe(e){return 0===e.indexOf("/")}function re(e){return oe(e)?e:"/"+e}function ie(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const se=e=>e>9?e:"0"+e;function ae({date:e=new Date,mode:t="date"}){return"time"===t?se(e.getHours())+":"+se(e.getMinutes()):e.getFullYear()+"-"+se(e.getMonth()+1)+"-"+se(e.getDate())}let le;function ce(){return le||(le=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),le)}function ue(e){if(!e)return;let t=e.type.name;for(;t&&Z($(t));)t=(e=e.parent).type.name;return e.proxy}function de(e){return 1===e.nodeType}function fe(e){const t=ce();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach((t=>{n[t]=e[t]})),N(n)}if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),N(t)}if(v(e))return W(e);if(p(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?W(o):fe(o);if(r)for(const e in r)t[e]=r[e]}return t}return N(e)}function pe(e){let t="";const n=ce();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach((n=>{e[n]&&(t+=n+" ")}));else if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(p(e))for(let o=0;o<e.length;o++){const n=pe(e[o]);n&&(t+=n+" ")}else t=z(e);return t.trim()}function he(e){return O(e.substring(5))}const ge=ie((e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[he(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[he(t)],o.call(this,t)}}));function me(e){return c({},e.dataset,e.__uniDataset)}const ve=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function ye(e){return{passive:e}}function _e(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:me(e),offsetTop:n,offsetLeft:o}}function be(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function we(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=be(e[n])}catch(o){t[n]=e[n]}})),t}const xe=/\+/g;function Se(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(xe," ");let r=e.indexOf("="),i=be(r<0?e:e.slice(0,r)),s=r<0?null:be(e.slice(r+1));if(i in t){let e=t[i];p(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Ce(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Te{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Ee=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const ke=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Oe=[];const Ae=ie(((e,t)=>t(e))),$e=function(){};$e.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Le=$e;const Pe={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Me(e,t,n){if(v(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in Pe?Pe[o]:o}return r}var o;return t}function Re(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach((i=>{const s=e[i];r[i]=S(s)?Re(s,t,n):p(s)?s.map((e=>S(e)?Re(e,t,n):Me(o,e))):Me(o,s,i)})),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Be,Ie;class je{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Be,!e&&Be&&(this.index=(Be.scopes||(Be.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Be;try{return Be=this,e()}finally{Be=t}}}on(){Be=this}off(){Be=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function De(e){return new je(e)}class Ne{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=Be){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Ue();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Ye()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=We,t=Ie;try{return We=!0,Ie=this,this._runnings++,Fe(this),this.fn()}finally{Ve(this),this._runnings--,Ie=t,We=e}}stop(){var e;this.active&&(Fe(this),Ve(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Fe(e){e._trackId++,e._depsLength=0}function Ve(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)He(e.deps[t],e);e.deps.length=e._depsLength}}function He(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let We=!0,ze=0;const qe=[];function Ue(){qe.push(We),We=!1}function Ye(){const e=qe.pop();We=void 0===e||e}function Xe(){ze++}function Ke(){for(ze--;!ze&&Ge.length;)Ge.shift()()}function Je(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&He(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Ge=[];function Qe(e,t,n){Xe();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&Ge.push(o.scheduler)))}Ke()}const Ze=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},et=new WeakMap,tt=Symbol(""),nt=Symbol("");function ot(e,t,n){if(We&&Ie){let t=et.get(e);t||et.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Ze((()=>t.delete(n)))),Je(Ie,o)}}function rt(e,t,n,o,r,i){const s=et.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&p(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!y(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":p(e)?C(n)&&a.push(s.get("length")):(a.push(s.get(tt)),h(e)&&a.push(s.get(nt)));break;case"delete":p(e)||(a.push(s.get(tt)),h(e)&&a.push(s.get(nt)));break;case"set":h(e)&&a.push(s.get(tt))}Xe();for(const l of a)l&&Qe(l,4);Ke()}const it=n("__proto__,__v_isRef,__isVue"),st=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),at=lt();function lt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Jt(this);for(let t=0,r=this.length;t<r;t++)ot(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Jt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Ue(),Xe();const n=Jt(this)[t].apply(this,e);return Ke(),Ye(),n}})),e}function ct(e){const t=Jt(this);return ot(t,0,e),t.hasOwnProperty(e)}class ut{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Ft:Nt:r?Dt:jt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=p(e);if(!o){if(i&&f(at,t))return Reflect.get(at,t,n);if("hasOwnProperty"===t)return ct}const s=Reflect.get(e,t,n);return(y(t)?st.has(t):it(t))?s:(o||ot(e,0,t),r?s:on(s)?i&&C(t)?s:s.value:_(s)?o?zt(s):Ht(s):s)}}class dt extends ut{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Yt(r);if(Xt(n)||Yt(n)||(r=Jt(r),n=Jt(n)),!p(e)&&on(r)&&!on(n))return!t&&(r.value=n,!0)}const i=p(e)&&C(t)?Number(t)<e.length:f(e,t),s=Reflect.set(e,t,n,o);return e===Jt(o)&&(i?M(n,r)&&rt(e,"set",t,n):rt(e,"add",t,n)),s}deleteProperty(e,t){const n=f(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&rt(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return y(t)&&st.has(t)||ot(e,0,t),n}ownKeys(e){return ot(e,0,p(e)?"length":tt),Reflect.ownKeys(e)}}class ft extends ut{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const pt=new dt,ht=new ft,gt=new dt(!0),mt=e=>e,vt=e=>Reflect.getPrototypeOf(e);function yt(e,t,n=!1,o=!1){const r=Jt(e=e.__v_raw),i=Jt(t);n||(M(t,i)&&ot(r,0,t),ot(r,0,i));const{has:s}=vt(r),a=o?mt:n?Zt:Qt;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function _t(e,t=!1){const n=this.__v_raw,o=Jt(n),r=Jt(e);return t||(M(e,r)&&ot(o,0,e),ot(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function bt(e,t=!1){return e=e.__v_raw,!t&&ot(Jt(e),0,tt),Reflect.get(e,"size",e)}function wt(e){e=Jt(e);const t=Jt(this);return vt(t).has.call(t,e)||(t.add(e),rt(t,"add",e,e)),this}function xt(e,t){t=Jt(t);const n=Jt(this),{has:o,get:r}=vt(n);let i=o.call(n,e);i||(e=Jt(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?M(t,s)&&rt(n,"set",e,t):rt(n,"add",e,t),this}function St(e){const t=Jt(this),{has:n,get:o}=vt(t);let r=n.call(t,e);r||(e=Jt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&rt(t,"delete",e,void 0),i}function Ct(){const e=Jt(this),t=0!==e.size,n=e.clear();return t&&rt(e,"clear",void 0,void 0),n}function Tt(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Jt(i),a=t?mt:e?Zt:Qt;return!e&&ot(s,0,tt),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function Et(e,t,n){return function(...o){const r=this.__v_raw,i=Jt(r),s=h(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?mt:t?Zt:Qt;return!t&&ot(i,0,l?nt:tt),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function kt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Ot(){const e={get(e){return yt(this,e)},get size(){return bt(this)},has:_t,add:wt,set:xt,delete:St,clear:Ct,forEach:Tt(!1,!1)},t={get(e){return yt(this,e,!1,!0)},get size(){return bt(this)},has:_t,add:wt,set:xt,delete:St,clear:Ct,forEach:Tt(!1,!0)},n={get(e){return yt(this,e,!0)},get size(){return bt(this,!0)},has(e){return _t.call(this,e,!0)},add:kt("add"),set:kt("set"),delete:kt("delete"),clear:kt("clear"),forEach:Tt(!0,!1)},o={get(e){return yt(this,e,!0,!0)},get size(){return bt(this,!0)},has(e){return _t.call(this,e,!0)},add:kt("add"),set:kt("set"),delete:kt("delete"),clear:kt("clear"),forEach:Tt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Et(r,!1,!1),n[r]=Et(r,!0,!1),t[r]=Et(r,!1,!0),o[r]=Et(r,!0,!0)})),[e,n,t,o]}const[At,$t,Lt,Pt]=Ot();function Mt(e,t){const n=t?e?Pt:Lt:e?$t:At;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(f(n,o)&&o in t?n:t,o,r)}const Rt={get:Mt(!1,!1)},Bt={get:Mt(!1,!0)},It={get:Mt(!0,!1)},jt=new WeakMap,Dt=new WeakMap,Nt=new WeakMap,Ft=new WeakMap;function Vt(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function Ht(e){return Yt(e)?e:qt(e,!1,pt,Rt,jt)}function Wt(e){return qt(e,!1,gt,Bt,Dt)}function zt(e){return qt(e,!0,ht,It,Nt)}function qt(e,t,n,o,r){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=Vt(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Ut(e){return Yt(e)?Ut(e.__v_raw):!(!e||!e.__v_isReactive)}function Yt(e){return!(!e||!e.__v_isReadonly)}function Xt(e){return!(!e||!e.__v_isShallow)}function Kt(e){return Ut(e)||Yt(e)}function Jt(e){const t=e&&e.__v_raw;return t?Jt(t):e}function Gt(e){return Object.isExtensible(e)&&B(e,"__v_skip",!0),e}const Qt=e=>_(e)?Ht(e):e,Zt=e=>_(e)?zt(e):e;class en{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ne((()=>e(this._value)),(()=>nn(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Jt(this);return e._cacheable&&!e.effect.dirty||!M(e._value,e._value=e.effect.run())||nn(e,4),tn(e),e.effect._dirtyLevel>=2&&nn(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function tn(e){var t;We&&Ie&&(e=Jt(e),Je(Ie,null!=(t=e.dep)?t:e.dep=Ze((()=>e.dep=void 0),e instanceof en?e:void 0)))}function nn(e,t=4,n){const o=(e=Jt(e)).dep;o&&Qe(o,t)}function on(e){return!(!e||!0!==e.__v_isRef)}function rn(e){return sn(e,!1)}function sn(e,t){return on(e)?e:new an(e,t)}class an{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Jt(e),this._value=t?e:Qt(e)}get value(){return tn(this),this._value}set value(e){const t=this.__v_isShallow||Xt(e)||Yt(e);e=t?e:Jt(e),M(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Qt(e),nn(this,4))}}function ln(e){return on(e)?e.value:e}const cn={get:(e,t,n)=>ln(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return on(r)&&!on(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function un(e){return Ut(e)?e:new Proxy(e,cn)}function dn(e,t,n,o){try{return o?e(...o):e()}catch(r){pn(r,t,n)}}function fn(e,t,n,o){if(m(e)){const r=dn(e,t,n,o);return r&&b(r)&&r.catch((e=>{pn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(fn(e[i],t,n,o));return r}function pn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void dn(s,null,10,[e,r,i])}hn(e,n,r,o)}function hn(e,t,n,o=!0){console.error(e)}let gn=!1,mn=!1;const vn=[];let yn=0;const _n=[];let bn=null,wn=0;const xn=Promise.resolve();let Sn=null;function Cn(e){const t=Sn||xn;return e?t.then(this?e.bind(this):e):t}function Tn(e){vn.length&&vn.includes(e,gn&&e.allowRecurse?yn+1:yn)||(null==e.id?vn.push(e):vn.splice(function(e){let t=yn+1,n=vn.length;for(;t<n;){const o=t+n>>>1,r=vn[o],i=An(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),En())}function En(){gn||mn||(mn=!0,Sn=xn.then(Ln))}function kn(e,t,n=(gn?yn+1:0)){for(;n<vn.length;n++){const t=vn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;vn.splice(n,1),n--,t()}}}function On(e){if(_n.length){const e=[...new Set(_n)].sort(((e,t)=>An(e)-An(t)));if(_n.length=0,bn)return void bn.push(...e);for(bn=e,wn=0;wn<bn.length;wn++)bn[wn]();bn=null,wn=0}}const An=e=>null==e.id?1/0:e.id,$n=(e,t)=>{const n=An(e)-An(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Ln(e){mn=!1,gn=!0,vn.sort($n);try{for(yn=0;yn<vn.length;yn++){const e=vn[yn];e&&!1!==e.active&&dn(e,null,14)}}finally{yn=0,vn.length=0,On(),gn=!1,Sn=null,(vn.length||_n.length)&&Ln()}}function Pn(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;let i=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in r){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=r[e]||o;s&&(i=n.map((e=>v(e)?e.trim():e))),t&&(i=n.map(I))}let l,c=r[l=P(t)]||r[l=P(O(t))];!c&&s&&(c=r[l=P($(t))]),c&&fn(c,e,6,Mn(e,c,i));const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,fn(u,e,6,Mn(e,u,i))}}function Mn(e,t,n){if(1!==n.length)return n;if(m(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&f(o,"type")&&f(o,"timeStamp")&&f(o,"target")&&f(o,"currentTarget")&&f(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Rn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!m(e)){const o=e=>{const n=Rn(e,t,!0);n&&(a=!0,c(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(p(i)?i.forEach((e=>s[e]=null)):c(s,i),_(e)&&o.set(e,s),s):(_(e)&&o.set(e,null),null)}function Bn(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),f(e,t[0].toLowerCase()+t.slice(1))||f(e,$(t))||f(e,t))}let In=null,jn=null;function Dn(e){const t=In;return In=e,jn=e&&e.type.__scopeId||null,t}function Nn(e,t=In,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Ur(-1);const r=Dn(t);let i;try{i=e(...n)}finally{Dn(r),o._d&&Ur(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Fn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:c,emit:u,render:d,renderCache:f,data:p,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const _=Dn(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=si(d.call(t,e,f,i,h,p,g)),y=c}else{const e=t;0,v=si(e.length>1?e(i,{attrs:c,slots:a,emit:u}):e(i,null)),y=t.props?c:Vn(c)}}catch(w){Hr.length=0,pn(w,e,1),v=ni(Fr)}let b=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(l)&&(y=Hn(y,s)),b=oi(b,y))}return n.dirs&&(b=oi(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),v=b,Dn(_),v}const Vn=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},Hn=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Wn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Bn(n,i))return!0}return!1}function zn(e,t){return Yn("components",e,!0,t)||e}const qn=Symbol.for("v-ndc");function Un(e){return v(e)?Yn("components",e,!1)||e:e||qn}function Yn(e,t,n=!0,o=!1){const r=In||pi;if(r){const n=r.type;if("components"===e){const e=Ci(n,!1);if(e&&(e===t||e===O(t)||e===L(O(t))))return n}const i=Xn(r[e]||n[e],t)||Xn(r.appContext[e],t);return!i&&o?n:i}}function Xn(e,t){return e&&(e[t]||e[O(t)]||e[L(O(t))])}const Kn=e=>e.__isSuspense;const Jn=Symbol.for("v-scx");function Gn(e,t){return eo(e,null,t)}const Qn={};function Zn(e,t,n){return eo(e,t,n)}function eo(e,t,{immediate:n,deep:r,flush:s,once:a,onTrack:l,onTrigger:c}=o){if(t&&a){const e=t;t=(...t)=>{e(...t),E()}}const d=pi,f=e=>!0===r?e:oo(e,!1===r?1:void 0);let h,g,v=!1,y=!1;if(on(e)?(h=()=>e.value,v=Xt(e)):Ut(e)?(h=()=>f(e),v=!0):p(e)?(y=!0,v=e.some((e=>Ut(e)||Xt(e))),h=()=>e.map((e=>on(e)?e.value:Ut(e)?f(e):m(e)?dn(e,d,2):void 0))):h=m(e)?t?()=>dn(e,d,2):()=>(g&&g(),fn(e,d,3,[b])):i,t&&r){const e=h;h=()=>oo(e())}let _,b=e=>{g=C.onStop=()=>{dn(e,d,4),g=C.onStop=void 0}};if(bi){if(b=i,t?n&&fn(t,d,3,[h(),y?[]:void 0,b]):h(),"sync"!==s)return i;{const e=yr(Jn);_=e.__watcherHandles||(e.__watcherHandles=[])}}let w=y?new Array(e.length).fill(Qn):Qn;const x=()=>{if(C.active&&C.dirty)if(t){const e=C.run();(r||v||(y?e.some(((e,t)=>M(e,w[t]))):M(e,w)))&&(g&&g(),fn(t,d,3,[e,w===Qn?void 0:y&&w[0]===Qn?[]:w,b]),w=e)}else C.run()};let S;x.allowRecurse=!!t,"sync"===s?S=x:"post"===s?S=()=>Pr(x,d&&d.suspense):(x.pre=!0,d&&(x.id=d.uid),S=()=>Tn(x));const C=new Ne(h,i,S),T=Be,E=()=>{C.stop(),T&&u(T.effects,C)};return t?n?x():w=C.run():"post"===s?Pr(C.run.bind(C),d&&d.suspense):C.run(),_&&_.push(E),E}function to(e,t,n){const o=this.proxy,r=v(e)?e.includes(".")?no(o,e):()=>o[e]:e.bind(o,o);let i;m(t)?i=t:(i=t.handler,n=t);const s=vi(this),a=eo(r,i.bind(o),n);return s(),a}function no(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function oo(e,t,n=0,o){if(!_(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),on(e))oo(e.value,t,n,o);else if(p(e))for(let r=0;r<e.length;r++)oo(e[r],t,n,o);else if(g(e)||h(e))e.forEach((e=>{oo(e,t,n,o)}));else if(S(e))for(const r in e)oo(e[r],t,n,o);return e}function ro(e,t){if(null===In)return e;const n=Si(In)||In.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,s,a,l=o]=t[i];e&&(m(e)&&(e={mounted:e,updated:e}),e.deep&&oo(s),r.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function io(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Ue(),fn(l,n,8,[e.el,a,e,t]),Ye())}}const so=Symbol("_leaveCb"),ao=Symbol("_enterCb");const lo=[Function,Array],co={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:lo,onEnter:lo,onAfterEnter:lo,onEnterCancelled:lo,onBeforeLeave:lo,onLeave:lo,onAfterLeave:lo,onLeaveCancelled:lo,onBeforeAppear:lo,onAppear:lo,onAfterAppear:lo,onAppearCancelled:lo},uo={name:"BaseTransition",props:co,setup(e,{slots:t}){const n=hi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Do((()=>{e.isMounted=!0})),Vo((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&vo(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==Fr){i=e;break}const s=Jt(e),{mode:a}=s;if(o.isLeaving)return ho(i);const l=go(i);if(!l)return ho(i);const c=po(l,s,o,n);mo(l,c);const u=n.subTree,d=u&&go(u);if(d&&d.type!==Fr&&!Gr(l,d)){const e=po(d,s,o,n);if(mo(d,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},ho(i);"in-out"===a&&l.type!==Fr&&(e.delayLeave=(e,t,n)=>{fo(o,d)[String(d.key)]=d,e[so]=()=>{t(),e[so]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function fo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function po(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:h,onLeaveCancelled:g,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:_}=t,b=String(e.key),w=fo(n,e),x=(e,t)=>{e&&fn(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),p(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},C={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=m||a}t[so]&&t[so](!0);const i=w[b];i&&Gr(e,i)&&i.el[so]&&i.el[so](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=_||u}let s=!1;const a=e[ao]=t=>{s||(s=!0,x(t?i:o,[e]),C.delayedLeave&&C.delayedLeave(),e[ao]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[ao]&&t[ao](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const s=t[so]=n=>{i||(i=!0,o(),x(n?g:h,[t]),t[so]=void 0,w[r]===e&&delete w[r])};w[r]=e,f?S(f,[t,s]):s()},clone:e=>po(e,t,n,o)};return C}function ho(e){if(xo(e))return(e=oi(e)).children=null,e}function go(e){return xo(e)?e.children?e.children[0]:void 0:e}function mo(e,t){6&e.shapeFlag&&e.component?mo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function vo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Dr?(128&s.patchFlag&&r++,o=o.concat(vo(s.children,t,a))):(t||s.type!==Fr)&&o.push(null!=a?oi(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function yo(e,t){return m(e)?(()=>c({name:e.name},t,{setup:e}))():e}const _o=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function bo(e){m(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return yo({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=pi;if(l)return()=>wo(l,e);const t=t=>{c=null,pn(t,e,13,!o)};if(s&&e.suspense||bi)return d().then((t=>()=>wo(t,e))).catch((e=>(t(e),()=>o?ni(o,{error:e}):null)));const a=rn(!1),u=rn(),f=rn(!!r);return r&&setTimeout((()=>{f.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{a.value=!0,e.parent&&xo(e.parent.vnode)&&(e.parent.effect.dirty=!0,Tn(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?wo(l,e):u.value&&o?ni(o,{error:u.value}):n&&!f.value?ni(n):void 0}})}function wo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=ni(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const xo=e=>e.type.__isKeepAlive;class So{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Co={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=hi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new So(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!Gr(t,i)||"key"===e.matchBy&&t.key!==i.key?(Lo(o=t),u(o,n,a,!0)):i&&Lo(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");function p(t){r.forEach(((n,o)=>{const i=Mo(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,R(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),Pr((()=>{i.isDeactivated=!1,i.a&&R(i.a);const t=e.props&&e.props.onVnodeMounted;t&&ui(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&Ro(t.bda),c(e,f,null,1,a),Pr((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&R(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&ui(n,t.parent,e),t.isDeactivated=!0}),a)},Zn((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&p((t=>Eo(e,t))),t&&p((e=>!Eo(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&r.set(h,Po(n.subTree))};return Do(g),Fo(g),Vo((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=Po(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&R(l.component.bda),Lo(l);const e=l.component.da;e&&Pr(e,a)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Jr(o)||!(4&o.shapeFlag)&&!Kn(o.type))return i=null,o;let s=Po(o);const a=s.type,l=Mo(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Eo(c,l))||u&&l&&Eo(u,l))return i=s,o;const d=null==s.key?a:s.key,f=r.get(d);return s.el&&(s=oi(s),Kn(o.type)&&(o.ssContent=s)),h=d,f&&(s.el=f.el,s.component=f.component,s.transition&&mo(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Kn(o.type)?o:s}}},To=Co;function Eo(e,t){return p(e)?e.some((e=>Eo(e,t))):v(e)?e.split(",").includes(t):"[object RegExp]"===x(e)&&e.test(t)}function ko(e,t){Ao(e,"a",t)}function Oo(e,t){Ao(e,"da",t)}function Ao(e,t,n=pi){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Bo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)xo(e.parent.vnode)&&$o(o,t,n,e),e=e.parent}}function $o(e,t,n,o){const r=Bo(t,e,o,!0);Ho((()=>{u(o[t],r)}),n)}function Lo(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Po(e){return Kn(e.type)?e.ssContent:e}function Mo(e,t){if("name"===t){const t=e.type;return Ci(_o(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Ro(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Bo(e,t,n=pi,o=!1){if(n){if(r=e,Ee.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;fn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Ue();const r=vi(n),i=fn(t,n,e,o);return r(),Ye(),i});return o?i.unshift(s):i.push(s),s}var r}const Io=e=>(t,n=pi)=>(!bi||"sp"===e)&&Bo(e,((...e)=>t(...e)),n),jo=Io("bm"),Do=Io("m"),No=Io("bu"),Fo=Io("u"),Vo=Io("bum"),Ho=Io("um"),Wo=Io("sp"),zo=Io("rtg"),qo=Io("rtc");function Uo(e,t=pi){Bo("ec",e,t)}function Yo(e,t,n,o){let r;const i=n&&n[o];if(p(e)||v(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(_(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Xo(e,t,n={},o,r){if(In.isCE||In.parent&&_o(In.parent)&&In.parent.isCE)return"default"!==t&&(n.name=t),ni("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),zr();const s=i&&Ko(i(n)),a=Kr(Dr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Ko(e){return e.some((e=>!Jr(e)||e.type!==Fr&&!(e.type===Dr&&!Ko(e.children))))?e:null}const Jo=e=>{if(!e)return null;if(_i(e)){return Si(e)||e.proxy}return Jo(e.parent)},Go=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Jo(e.parent),$root:e=>Jo(e.root),$emit:e=>e.emit,$options:e=>ir(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,Tn(e.update)})(e)),$nextTick:e=>e.n||(e.n=Cn.bind(e.proxy)),$watch:e=>to.bind(e)}),Qo=(e,t)=>e!==o&&!e.__isScriptSetup&&f(e,t),Zo={get({_:e},t){const{ctx:n,setupState:r,data:i,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return s[t]}else{if(Qo(r,t))return a[t]=1,r[t];if(i!==o&&f(i,t))return a[t]=2,i[t];if((u=e.propsOptions[0])&&f(u,t))return a[t]=3,s[t];if(n!==o&&f(n,t))return a[t]=4,n[t];tr&&(a[t]=0)}}const d=Go[t];let p,h;return d?("$attrs"===t&&ot(e,0,t),d(e)):(p=l.__cssModules)&&(p=p[t])?p:n!==o&&f(n,t)?(a[t]=4,n[t]):(h=c.config.globalProperties,f(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:s}=e;return Qo(i,t)?(i[t]=n,!0):r!==o&&f(r,t)?(r[t]=n,!0):!f(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:s}},a){let l;return!!n[a]||e!==o&&f(e,a)||Qo(t,a)||(l=s[0])&&f(l,a)||f(r,a)||f(Go,a)||f(i.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:f(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function er(e){return p(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let tr=!0;function nr(e){const t=ir(e),n=e.proxy,o=e.ctx;tr=!1,t.beforeCreate&&or(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:a,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:C,render:T,renderTracked:E,renderTriggered:k,errorCaptured:O,serverPrefetch:A,expose:$,inheritAttrs:L,components:P,directives:M,filters:R}=t;if(u&&function(e,t,n=i){p(e)&&(e=cr(e));for(const o in e){const n=e[o];let r;r=_(n)?"default"in n?yr(n.from||o,n.default,!0):yr(n.from||o):yr(n),on(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),a)for(const i in a){const e=a[i];m(e)&&(o[i]=e.bind(n))}if(r){const t=r.call(n,n);_(t)&&(e.data=Ht(t))}if(tr=!0,s)for(const p in s){const e=s[p],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):i,r=!m(e)&&m(e.set)?e.set.bind(n):i,a=Ti({get:t,set:r});Object.defineProperty(o,p,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const i in l)rr(l[i],o,n,i);if(c){const e=m(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{vr(t,e[t])}))}function B(e,t){p(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&or(d,e,"c"),B(jo,f),B(Do,h),B(No,g),B(Fo,v),B(ko,y),B(Oo,b),B(Uo,O),B(qo,E),B(zo,k),B(Vo,x),B(Ho,C),B(Wo,A),p($))if($.length){const t=e.exposed||(e.exposed={});$.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});T&&e.render===i&&(e.render=T),null!=L&&(e.inheritAttrs=L),P&&(e.components=P),M&&(e.directives=M);const I=e.appContext.config.globalProperties.$applyOptions;I&&I(t,e,n)}function or(e,t,n){fn(p(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function rr(e,t,n,o){const r=o.includes(".")?no(n,o):()=>n[o];if(v(e)){const n=t[e];m(n)&&Zn(r,n)}else if(m(e))Zn(r,e.bind(n));else if(_(e))if(p(e))e.forEach((e=>rr(e,t,n,o)));else{const o=m(e.handler)?e.handler.bind(n):t[e.handler];m(o)&&Zn(r,o,e)}}function ir(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>sr(l,e,s,!0))),sr(l,t,s)):l=t,_(t)&&i.set(t,l),l}function sr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&sr(e,i,n,!0),r&&r.forEach((t=>sr(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=ar[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const ar={data:lr,props:fr,emits:fr,methods:dr,computed:dr,beforeCreate:ur,created:ur,beforeMount:ur,mounted:ur,beforeUpdate:ur,updated:ur,beforeDestroy:ur,beforeUnmount:ur,destroyed:ur,unmounted:ur,activated:ur,deactivated:ur,errorCaptured:ur,serverPrefetch:ur,components:dr,directives:dr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=ur(e[o],t[o]);return n},provide:lr,inject:function(e,t){return dr(cr(e),cr(t))}};function lr(e,t){return t?e?function(){return c(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function cr(e){if(p(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ur(e,t){return e?[...new Set([].concat(e,t))]:t}function dr(e,t){return e?c(Object.create(null),e,t):t}function fr(e,t){return e?p(e)&&p(t)?[...new Set([...e,...t])]:c(Object.create(null),er(e),er(null!=t?t:{})):t}function pr(){return{app:null,config:{isNativeTag:s,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let hr=0;function gr(e,t){return function(n,o=null){m(n)||(n=c({},n)),null==o||_(o)||(o=null);const r=pr(),i=new WeakSet;let s=!1;const a=r.app={_uid:hr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:ki,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&m(e.install)?(i.add(e),e.install(a,...t)):m(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=ni(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,Si(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=mr;mr=a;try{return e()}finally{mr=t}}};return a}}let mr=null;function vr(e,t){if(pi){let n=pi.provides;const o=pi.parent&&pi.parent.provides;o===n&&(n=pi.provides=Object.create(o)),n[e]=t,"app"===pi.type.mpType&&pi.appContext.app.provide(e,t)}else;}function yr(e,t,n=!1){const o=pi||In;if(o||mr){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:mr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&m(t)?t.call(o&&o.proxy):t}}function _r(e,t,n,r){const[i,s]=e.propsOptions;let a,l=!1;if(t)for(let o in t){if(T(o))continue;const c=t[o];let u;i&&f(i,u=O(o))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:Bn(e.emitsOptions,o)||o in r&&c===r[o]||(r[o]=c,l=!0)}if(s){const t=Jt(n),r=a||o;for(let o=0;o<s.length;o++){const a=s[o];n[a]=br(i,t,a,r[a],e,!f(r,a))}}return l}function br(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=f(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&m(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=vi(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==$(n)||(o=!0))}return o}function wr(e,t,n=!1){const i=t.propsCache,s=i.get(e);if(s)return s;const a=e.props,l={},u=[];let d=!1;if(!m(e)){const o=e=>{d=!0;const[n,o]=wr(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!d)return _(e)&&i.set(e,r),r;if(p(a))for(let r=0;r<a.length;r++){const e=O(a[r]);xr(e)&&(l[e]=o)}else if(a)for(const o in a){const e=O(o);if(xr(e)){const t=a[o],n=l[e]=p(t)||m(t)?{type:t}:c({},t);if(n){const t=Tr(Boolean,n.type),o=Tr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||f(n,"default"))&&u.push(e)}}}const h=[l,u];return _(e)&&i.set(e,h),h}function xr(e){return"$"!==e[0]&&!T(e)}function Sr(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Cr(e,t){return Sr(e)===Sr(t)}function Tr(e,t){return p(t)?t.findIndex((t=>Cr(t,e))):m(t)&&Cr(t,e)?0:-1}const Er=e=>"_"===e[0]||"$stable"===e,kr=e=>p(e)?e.map(si):[si(e)],Or=(e,t,n)=>{if(t._n)return t;const o=Nn(((...e)=>kr(t(...e))),n);return o._c=!1,o},Ar=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Er(r))continue;const n=e[r];if(m(n))t[r]=Or(0,n,o);else if(null!=n){const e=kr(n);t[r]=()=>e}}},$r=(e,t)=>{const n=kr(t);e.slots.default=()=>n};function Lr(e,t,n,r,i=!1){if(p(e))return void e.forEach(((e,o)=>Lr(e,t&&(p(t)?t[o]:t),n,r,i)));if(_o(r)&&!i)return;const s=4&r.shapeFlag?Si(r.component)||r.component.proxy:r.el,a=i?null:s,{i:l,r:c}=e,d=t&&t.r,h=l.refs===o?l.refs={}:l.refs,g=l.setupState;if(null!=d&&d!==c&&(v(d)?(h[d]=null,f(g,d)&&(g[d]=null)):on(d)&&(d.value=null)),m(c))dn(c,l,12,[a,h]);else{const t=v(c),o=on(c);if(t||o){const r=()=>{if(e.f){const n=t?f(g,c)?g[c]:h[c]:c.value;i?p(n)&&u(n,s):p(n)?n.includes(s)||n.push(s):t?(h[c]=[s],f(g,c)&&(g[c]=h[c])):(c.value=[s],e.k&&(h[e.k]=c.value))}else t?(h[c]=a,f(g,c)&&(g[c]=a)):o&&(c.value=a,e.k&&(h[e.k]=a))};a?(r.id=-1,Pr(r,n)):r()}}}const Pr=function(e,t){var n;t&&t.pendingBranch?p(e)?t.effects.push(...e):t.effects.push(e):(p(n=e)?_n.push(...n):bn&&bn.includes(n,n.allowRecurse?wn+1:wn)||_n.push(n),En())};function Mr(e){return function(e,t){D().__VUE__=!0;const{insert:n,remove:s,patchProp:a,forcePatchProp:l,createElement:u,createText:d,createComment:p,setText:h,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=i,insertStaticContent:_}=e,w=(e,t,n,o=null,r=null,i=null,s,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Gr(e,t)&&(o=te(e),J(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Nr:x(e,t,n,o);break;case Fr:S(e,t,n,o);break;case Vr:null==e&&C(t,n,o,s);break;case Dr:F(e,t,n,o,r,i,s,a,l);break;default:1&d?A(e,t,n,o,r,i,s,a,l):6&d?V(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,re)}null!=u&&r&&Lr(u,e&&e.ref,i,t||e,!t)},x=(e,t,o,r)=>{if(null==e)n(t.el=d(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&h(n,t.children)}},S=(e,t,o,r)=>{null==e?n(t.el=p(t.children||""),o,r):t.el=e.el},C=(e,t,n,o)=>{[e.el,e.anchor]=_(e.children,t,n,o,e.el,e.anchor)},E=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},k=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),s(e),e=n;s(t)},A=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?L(t,n,o,r,i,s,a,l):I(e,t,r,i,s,a,l)},L=(e,t,o,r,i,s,l,c)=>{let d,f;const{props:p,shapeFlag:h,transition:m,dirs:v}=e;if(d=e.el=u(e.type,s,p&&p.is,p),8&h?g(d,e.children):16&h&&M(e.children,d,null,r,i,Rr(e,s),l,c),v&&io(e,null,r,"created"),P(d,e,e.scopeId,l,r),p){for(const t in p)"value"===t||T(t)||a(d,t,null,p[t],s,e.children,r,i,ee);"value"in p&&a(d,"value",null,p.value,s),(f=p.onVnodeBeforeMount)&&ui(f,r,e)}Object.defineProperty(d,"__vueParentComponent",{value:r,enumerable:!1}),v&&io(e,null,r,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(i,m);y&&m.beforeEnter(d),n(d,t,o),((f=p&&p.onVnodeMounted)||y||v)&&Pr((()=>{f&&ui(f,r,e),y&&m.enter(d),v&&io(e,null,r,"mounted")}),i)},P=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;P(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},M=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?ai(e[c]):si(e[c]);w(null,l,t,n,o,r,i,s,a)}},I=(e,t,n,r,i,s,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:p}=t;d|=16&e.patchFlag;const h=e.props||o,m=t.props||o;let v;if(n&&Br(n,!1),(v=m.onVnodeBeforeUpdate)&&ui(v,n,t,e),p&&io(t,e,n,"beforeUpdate"),n&&Br(n,!0),f?j(e.dynamicChildren,f,u,n,r,Rr(t,i),s):c||U(e,t,u,null,n,r,Rr(t,i),s,!1),d>0){if(16&d)N(u,t,h,m,n,r,i);else if(2&d&&h.class!==m.class&&a(u,"class",null,m.class,i),4&d&&a(u,"style",h.style,m.style,i),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const s=o[t],c=h[s],d=m[s];(d!==c||"value"===s||l&&l(u,s))&&a(u,s,c,d,i,e.children,n,r,ee)}}1&d&&e.children!==t.children&&g(u,t.children)}else c||null!=f||N(u,t,h,m,n,r,i);((v=m.onVnodeUpdated)||p)&&Pr((()=>{v&&ui(v,n,t,e),p&&io(t,e,n,"updated")}),r)},j=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Dr||!Gr(l,c)||70&l.shapeFlag)?m(l.el):n;w(l,c,u,null,o,r,i,s,!0)}},N=(e,t,n,r,i,s,c)=>{if(n!==r){if(n!==o)for(const o in n)T(o)||o in r||a(e,o,n[o],null,c,t.children,i,s,ee);for(const o in r){if(T(o))continue;const u=r[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&a(e,o,d,u,c,t.children,i,s,ee)}"value"in r&&a(e,"value",n.value,r.value,c)}},F=(e,t,o,r,i,s,a,l,c)=>{const u=t.el=e?e.el:d(""),f=t.anchor=e?e.anchor:d("");let{patchFlag:p,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(u,o,r),n(f,o,r),M(t.children||[],o,f,i,s,a,l,c)):p>0&&64&p&&h&&e.dynamicChildren?(j(e.dynamicChildren,h,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Ir(e,t,!0)):U(e,t,o,f,i,s,a,l,c)},V=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):H(t,n,o,r,i,s,l):W(e,t,l)},H=(e,t,n,r,i,s,a)=>{const l=e.component=function(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||di,s={uid:fi++,vnode:e,type:r,parent:t,appContext:i,get renderer(){return"app"===r.mpType?"app":this.$pageInstance&&this.$pageInstance==s?"page":"component"},root:null,next:null,subTree:null,effect:null,update:null,scope:new je(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:wr(r,i),emitsOptions:Rn(r,i),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=Pn.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,r,i);if(xo(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&mi(t);const{props:n,children:o}=e.vnode,r=_i(e);(function(e,t,n,o=!1){const r={},i={};B(i,Qr,1),e.propsDefaults=Object.create(null),_r(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Wt(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Jt(t),B(t,"_",n)):Ar(t,e.slots={})}else e.slots={},t&&$r(e,t);B(e.slots,Qr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Gt(new Proxy(e.ctx,Zo));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(ot(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=vi(e);Ue();const i=dn(o,e,0,[e.props,n]);if(Ye(),r(),b(i)){if(i.then(yi,yi),t)return i.then((n=>{wi(e,n,t)})).catch((t=>{pn(t,e,0)}));e.asyncDep=i}else wi(e,i,t)}else xi(e,t)}(e,t):void 0;t&&mi(!1)}(l),l.asyncDep){if(i&&i.registerDep(l,z),!e.el){const e=l.subTree=ni(Fr);S(null,e,t,n)}}else z(l,e,t,n,i,s,a)},W=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||Wn(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?Wn(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!Bn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void q(o,t,n);o.next=t,function(e){const t=vn.indexOf(e);t>yn&&vn.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},z=(e,t,n,o,r,s,a)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:i,vnode:c}=e;{const n=jr(e);if(n)return t&&(t.el=c.el,q(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,d=t;Br(e,!1),t?(t.el=c.el,q(e,t,a)):t=c,n&&R(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&ui(u,i,t,c),Br(e,!0);const f=Fn(e),p=e.subTree;e.subTree=f,w(p,f,m(p.el),te(p),e,r,s),t.el=f.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,f.el),o&&Pr(o,r),(u=t.props&&t.props.onVnodeUpdated)&&Pr((()=>ui(u,i,t,c)),r)}else{let i;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,f=_o(t);if(Br(e,!1),c&&R(c),!f&&(i=l&&l.onVnodeBeforeMount)&&ui(i,d,t),Br(e,!0),a&&se){const n=()=>{e.subTree=Fn(e),se(a,e.subTree,e,r,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const i=e.subTree=Fn(e);w(null,i,n,o,e,r,s),t.el=i.el}if(u&&Pr(u,r),!f&&(i=l&&l.onVnodeMounted)){const e=t;Pr((()=>ui(i,d,e)),r)}(256&t.shapeFlag||d&&_o(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&Ro(e.ba),e.a&&Pr(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new Ne(l,i,(()=>Tn(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Br(e,!0),u()},q=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Jt(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;_r(e,t,r,i)&&(c=!0);for(const i in a)t&&(f(t,i)||(o=$(i))!==i&&f(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=br(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&f(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(Bn(e.emitsOptions,s))continue;const u=t[s];if(l)if(f(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=O(s);r[t]=br(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&rt(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{const{vnode:r,slots:i}=e;let s=!0,a=o;if(32&r.shapeFlag){const e=t._;e?n&&1===e?s=!1:(c(i,t),n||1!==e||delete i._):(s=!t.$stable,Ar(t,i)),a=t}else t&&($r(e,t),a={default:1});if(s)for(const o in i)Er(o)||null!=a[o]||delete i[o]})(e,t.children,n),Ue(),kn(e),Ye()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(128&f)return void X(c,d,n,o,r,i,s,a,l);if(256&f)return void Y(c,d,n,o,r,i,s,a,l)}8&p?(16&u&&ee(c,r,i),d!==c&&g(n,d)):16&u?16&p?X(c,d,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&u&&g(n,""),16&p&&M(d,n,o,r,i,s,a,l))},Y=(e,t,n,o,i,s,a,l,c)=>{t=t||r;const u=(e=e||r).length,d=t.length,f=Math.min(u,d);let p;for(p=0;p<f;p++){const o=t[p]=c?ai(t[p]):si(t[p]);w(e[p],o,n,null,i,s,a,l,c)}u>d?ee(e,i,s,!0,!1,f):M(t,n,o,i,s,a,l,c,f)},X=(e,t,n,o,i,s,a,l,c)=>{let u=0;const d=t.length;let f=e.length-1,p=d-1;for(;u<=f&&u<=p;){const o=e[u],r=t[u]=c?ai(t[u]):si(t[u]);if(!Gr(o,r))break;w(o,r,n,null,i,s,a,l,c),u++}for(;u<=f&&u<=p;){const o=e[f],r=t[p]=c?ai(t[p]):si(t[p]);if(!Gr(o,r))break;w(o,r,n,null,i,s,a,l,c),f--,p--}if(u>f){if(u<=p){const e=p+1,r=e<d?t[e].el:o;for(;u<=p;)w(null,t[u]=c?ai(t[u]):si(t[u]),n,r,i,s,a,l,c),u++}}else if(u>p)for(;u<=f;)J(e[u],i,s,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=p;u++){const e=t[u]=c?ai(t[u]):si(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const _=p-g+1;let b=!1,x=0;const S=new Array(_);for(u=0;u<_;u++)S[u]=0;for(u=h;u<=f;u++){const o=e[u];if(y>=_){J(o,i,s,!0);continue}let r;if(null!=o.key)r=m.get(o.key);else for(v=g;v<=p;v++)if(0===S[v-g]&&Gr(o,t[v])){r=v;break}void 0===r?J(o,i,s,!0):(S[r-g]=u+1,r>=x?x=r:b=!0,w(o,t[r],n,null,i,s,a,l,c),y++)}const C=b?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):r;for(v=C.length-1,u=_-1;u>=0;u--){const e=g+u,r=t[e],f=e+1<d?t[e+1].el:o;0===S[u]?w(null,r,n,f,i,s,a,l,c):b&&(v<0||u!==C[v]?K(r,n,f,2):v--)}}},K=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void K(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,re);if(a===Dr){n(s,t,o);for(let e=0;e<c.length;e++)K(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===Vr)return void E(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Pr((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},J=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=a&&Lr(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&f,h=!_o(e);let g;if(h&&(g=s&&s.onVnodeBeforeUnmount)&&ui(g,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);p&&io(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==Dr||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Dr&&384&d||!r&&16&u)&&ee(l,t,n),o&&G(e)}(h&&(g=s&&s.onVnodeUnmounted)||p)&&Pr((()=>{g&&ui(g,t,e),p&&io(e,null,t,"unmounted")}),n)},G=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===Dr)return void Q(n,o);if(t===Vr)return void k(e);const i=()=>{s(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,s=()=>t(n,i);o?o(e.el,i,s):s()}else i()},Q=(e,t)=>{let n;for(;e!==t;)n=v(e),s(e),e=n;s(t)},Z=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&R(o),r.stop(),i&&(i.active=!1,J(s,e,t,n)),a&&Pr(a,t),Pr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)J(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&J(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,kn(),On(),ne=!1),t._vnode=e},re={p:w,um:J,m:K,r:G,mt:H,mc:M,pc:U,pbc:j,n:te,o:e};let ie,se;t&&([ie,se]=t(re));return{render:oe,hydrate:ie,createApp:gr(oe,ie)}}(e)}function Rr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Br({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Ir(e,t,n=!1){const o=e.children,r=t.children;if(p(o)&&p(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=ai(r[i]),t.el=e.el),n||Ir(e,t)),t.type===Nr&&(t.el=e.el)}}function jr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:jr(t)}const Dr=Symbol.for("v-fgt"),Nr=Symbol.for("v-txt"),Fr=Symbol.for("v-cmt"),Vr=Symbol.for("v-stc"),Hr=[];let Wr=null;function zr(e=!1){Hr.push(Wr=e?null:[])}let qr=1;function Ur(e){qr+=e}function Yr(e){return e.dynamicChildren=qr>0?Wr||r:null,Hr.pop(),Wr=Hr[Hr.length-1]||null,qr>0&&Wr&&Wr.push(e),e}function Xr(e,t,n,o,r,i){return Yr(ti(e,t,n,o,r,i,!0))}function Kr(e,t,n,o,r){return Yr(ni(e,t,n,o,r,!0))}function Jr(e){return!!e&&!0===e.__v_isVNode}function Gr(e,t){return e.type===t.type&&e.key===t.key}const Qr="__vInternal",Zr=({key:e})=>null!=e?e:null,ei=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||on(e)||m(e)?{i:In,r:e,k:t,f:!!n}:e:null);function ti(e,t=null,n=null,o=0,r=null,i=(e===Dr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Zr(t),ref:t&&ei(t),scopeId:jn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:In};return a?(li(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),qr>0&&!s&&Wr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Wr.push(l),l}const ni=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==qn||(e=Fr);if(Jr(e)){const o=oi(e,t,!0);return n&&li(o,n),qr>0&&!i&&Wr&&(6&o.shapeFlag?Wr[Wr.indexOf(e)]=o:Wr.push(o)),o.patchFlag|=-2,o}s=e,m(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Kt(e)||Qr in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=pe(e)),_(n)&&(Kt(n)&&!p(n)&&(n=c({},n)),t.style=fe(n))}const a=v(e)?1:Kn(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:m(e)?2:0;return ti(e,t,n,o,r,a,i,!0)};function oi(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?ci(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Zr(a),ref:t&&t.ref?n&&r?p(r)?r.concat(ei(t)):[r,ei(t)]:ei(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Dr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&oi(e.ssContent),ssFallback:e.ssFallback&&oi(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function ri(e=" ",t=0){return ni(Nr,null,e,t)}function ii(e="",t=!1){return t?(zr(),Kr(Fr,null,e)):ni(Fr,null,e)}function si(e){return null==e||"boolean"==typeof e?ni(Fr):p(e)?ni(Dr,null,e.slice()):"object"==typeof e?ai(e):ni(Nr,null,String(e))}function ai(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:oi(e)}function li(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(p(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),li(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Qr in t?3===o&&In&&(1===In.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=In}}else m(t)?(t={default:t,_ctx:In},n=32):(t=String(t),64&o?(n=16,t=[ri(t)]):n=8);e.children=t,e.shapeFlag|=n}function ci(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=pe([t.class,o.class]));else if("style"===e)t.style=fe([t.style,o.style]);else if(a(e)){const n=t[e],r=o[e];!r||n===r||p(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function ui(e,t,n,o=null){fn(e,t,7,[n,o])}const di=pr();let fi=0;let pi=null;const hi=()=>pi||In;let gi,mi;{const e=D(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};gi=t("__VUE_INSTANCE_SETTERS__",(e=>pi=e)),mi=t("__VUE_SSR_SETTERS__",(e=>bi=e))}const vi=e=>{const t=pi;return gi(e),e.scope.on(),()=>{e.scope.off(),gi(t)}},yi=()=>{pi&&pi.scope.off(),gi(null)};function _i(e){return 4&e.vnode.shapeFlag}let bi=!1;function wi(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=un(t)),xi(e,n)}function xi(e,t,n){const o=e.type;e.render||(e.render=o.render||i);{const t=vi(e);Ue();try{nr(e)}finally{Ye(),t()}}}function Si(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(un(Gt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Go?Go[n](e):void 0,has:(e,t)=>t in e||t in Go}))}function Ci(e,t=!0){return m(e)?e.displayName||e.name:e.name||t&&e.__name}const Ti=(e,t)=>{const n=function(e,t,n=!1){let o,r;const s=m(e);return s?(o=e,r=i):(o=e.get,r=e.set),new en(o,r,s||!r,n)}(e,0,bi);return n};function Ei(e,t,n){const o=arguments.length;return 2===o?_(t)&&!p(t)?Jr(t)?ni(e,null,[t]):ni(e,t):ni(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Jr(n)&&(n=[n]),ni(e,t,n))}const ki="3.4.21",Oi="undefined"!=typeof document?document:null,Ai=Oi&&Oi.createElement("template"),$i={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?Oi.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Oi.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Oi.createElement(e,{is:n}):Oi.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Oi.createTextNode(e),createComment:e=>Oi.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Oi.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Ai.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=Ai.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Li="transition",Pi=Symbol("_vtc"),Mi=(e,{slots:t})=>Ei(uo,function(e){const t={};for(const c in e)c in Ri||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=s,appearToClass:d=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(_(e))return[ji(e.enter),ji(e.leave)];{const t=ji(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:C=y,onAppear:T=b,onAppearCancelled:E=w}=t,k=(e,t,n)=>{Ni(e,t?d:a),Ni(e,t?u:s),n&&n()},O=(e,t)=>{e._isLeaving=!1,Ni(e,f),Ni(e,h),Ni(e,p),t&&t()},A=e=>(t,n)=>{const r=e?T:b,s=()=>k(t,e,n);Bi(r,[t,s]),Fi((()=>{Ni(t,e?l:i),Di(t,e?d:a),Ii(r)||Hi(t,o,m,s)}))};return c(t,{onBeforeEnter(e){Bi(y,[e]),Di(e,i),Di(e,s)},onBeforeAppear(e){Bi(C,[e]),Di(e,l),Di(e,u)},onEnter:A(!1),onAppear:A(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>O(e,t);Di(e,f),document.body.offsetHeight,Di(e,p),Fi((()=>{e._isLeaving&&(Ni(e,f),Di(e,h),Ii(x)||Hi(e,o,v,n))})),Bi(x,[e,n])},onEnterCancelled(e){k(e,!1),Bi(w,[e])},onAppearCancelled(e){k(e,!0),Bi(E,[e])},onLeaveCancelled(e){O(e),Bi(S,[e])}})}(e),t);Mi.displayName="Transition";const Ri={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Mi.props=c({},co,Ri);const Bi=(e,t=[])=>{p(e)?e.forEach((e=>e(...t))):e&&e(...t)},Ii=e=>!!e&&(p(e)?e.some((e=>e.length>1)):e.length>1);function ji(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Di(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Pi]||(e[Pi]=new Set)).add(t)}function Ni(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Pi];n&&(n.delete(t),n.size||(e[Pi]=void 0))}function Fi(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Vi=0;function Hi(e,t,n,o){const r=e._endId=++Vi,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=Wi(r,i),a=o("animationDelay"),l=o("animationDuration"),c=Wi(a,l);let u=null,d=0,f=0;t===Li?s>0&&(u=Li,d=s,f=i.length):"animation"===t?c>0&&(u="animation",d=c,f=l.length):(d=Math.max(s,c),u=d>0?s>c?Li:"animation":null,f=u?u===Li?i.length:l.length:0);const p=u===Li&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:f,hasTransform:p}}(e,t);if(!s)return o();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),a+1),e.addEventListener(c,f)}function Wi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>zi(t)+zi(e[n]))))}function zi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const qi=Symbol("_vod"),Ui=Symbol("_vsh"),Yi={beforeMount(e,{value:t},{transition:n}){e[qi]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Xi(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Xi(e,!0),o.enter(e)):o.leave(e,(()=>{Xi(e,!1)})):Xi(e,t))},beforeUnmount(e,{value:t}){Xi(e,t)}};function Xi(e,t){e.style.display=t?e[qi]:"none",e[Ui]=!t}const Ki=Symbol(""),Ji=/(^|;)\s*display\s*:/;const Gi=/\s*!important$/;function Qi(e,t,n){if(p(n))n.forEach((n=>Qi(e,t,n)));else if(null==n&&(n=""),n=ls(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=es[t];if(n)return n;let o=O(t);if("filter"!==o&&o in e)return es[t]=o;o=L(o);for(let r=0;r<Zi.length;r++){const n=Zi[r]+o;if(n in e)return es[t]=n}return t}(e,t);Gi.test(n)?e.setProperty($(o),n.replace(Gi,""),"important"):e[o]=n}}const Zi=["Webkit","Moz","ms"],es={};const{unit:ts,unitRatio:ns,unitPrecision:os}={unit:"rem",unitRatio:10/320,unitPrecision:5},rs=(is=ts,ss=ns,as=os,e=>e.replace(ve,((e,t)=>{if(!t)return e;if(1===ss)return`${t}${is}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*ss,as);return 0===n?"0":`${n}${is}`})));var is,ss,as;const ls=e=>v(e)?rs(e):e,cs="http://www.w3.org/1999/xlink";const us=Symbol("_vei");function ds(e,t,n,o,r=null){const i=e[us]||(e[us]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(fs.test(e)){let n;for(t={};n=e.match(fs);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):$(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&p(i)){const n=gs(e,i);for(let o=0;o<n.length;o++){const i=n[o];fn(i,t,5,i.__wwe?[e]:r(e))}}else fn(gs(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>ps||(hs.then((()=>ps=0)),ps=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const fs=/(?:Once|Passive|Capture)$/;let ps=0;const hs=Promise.resolve();function gs(e,t){if(p(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const ms=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const vs=["ctrl","shift","alt","meta"],ys={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>vs.some((n=>e[`${n}Key`]&&!t.includes(n)))},_s=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=ys[t[e]];if(o&&o(n,t))return}return e(n,...o)})},bs=c({patchProp:(e,t,n,o,r,i,s,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;Cn((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[Pi];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=v(n);let i=!1;if(n&&!r){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Qi(o,t,"")}else for(const e in t)null==n[e]&&Qi(o,e,"");for(const e in n)"display"===e&&(i=!0),Qi(o,e,n[e])}else if(r){if(t!==n){const e=o[Ki];e&&(n+=";"+e),o.cssText=n,i=Ji.test(n)}}else t&&e.removeAttribute("style");qi in e&&(e[qi]=i?o.display:"",e[Ui]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)Qi(o,a,s[a])}(e,n,o):a(t)?l(t)||ds(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&ms(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(ms(t)&&v(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=U(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(cs,t.slice(6,t.length)):e.setAttributeNS(cs,t,n);else{const o=q(t);null==n||o&&!U(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},$i);let ws;const xs=(...e)=>{const t=(ws||(ws=Mr(bs))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(v(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;m(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Ss="undefined"!=typeof document;const Cs=Object.assign;function Ts(e,t){const n={};for(const o in t){const r=t[o];n[o]=ks(r)?r.map(e):e(r)}return n}const Es=()=>{},ks=Array.isArray,Os=/#/g,As=/&/g,$s=/\//g,Ls=/=/g,Ps=/\?/g,Ms=/\+/g,Rs=/%5B/g,Bs=/%5D/g,Is=/%5E/g,js=/%60/g,Ds=/%7B/g,Ns=/%7C/g,Fs=/%7D/g,Vs=/%20/g;function Hs(e){return encodeURI(""+e).replace(Ns,"|").replace(Rs,"[").replace(Bs,"]")}function Ws(e){return Hs(e).replace(Ms,"%2B").replace(Vs,"+").replace(Os,"%23").replace(As,"%26").replace(js,"`").replace(Ds,"{").replace(Fs,"}").replace(Is,"^")}function zs(e){return null==e?"":function(e){return Hs(e).replace(Os,"%23").replace(Ps,"%3F")}(e).replace($s,"%2F")}function qs(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Us=/\/$/;function Ys(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:qs(s)}}function Xs(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Ks(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Js(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Gs(e[n],t[n]))return!1;return!0}function Gs(e,t){return ks(e)?Qs(e,t):ks(t)?Qs(t,e):e===t}function Qs(e,t){return ks(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var Zs,ea,ta,na;function oa(e){if(!e)if(Ss){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Us,"")}(ea=Zs||(Zs={})).pop="pop",ea.push="push",(na=ta||(ta={})).back="back",na.forward="forward",na.unknown="";const ra=/^[^#]+#/;function ia(e,t){return e.replace(ra,"#")+t}const sa=()=>({left:window.scrollX,top:window.scrollY});function aa(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function la(e,t){return(history.state?history.state.position-t:-1)+e}const ca=new Map;function ua(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Xs(n,"")}return Xs(n,e)+o+r}function da(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?sa():null}}function fa(e){const{history:t,location:n}=window,o={value:ua(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=Cs({},r.value,t.state,{forward:e,scroll:sa()});i(s.current,s,!0),i(e,Cs({},da(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Cs({},t.state,da(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function pa(e){const t=fa(e=oa(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=ua(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:Zs.pop,direction:u?u>0?ta.forward:ta.back:ta.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Cs({},e.state,{scroll:sa()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Cs({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:ia.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ha(e){return"string"==typeof e||"symbol"==typeof e}const ga={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},ma=Symbol("");var va,ya;function _a(e,t){return Cs(new Error,{type:e,[ma]:!0},t)}function ba(e,t){return e instanceof Error&&ma in e&&(null==t||!!(e.type&t))}(ya=va||(va={}))[ya.aborted=4]="aborted",ya[ya.cancelled=8]="cancelled",ya[ya.duplicated=16]="duplicated";const wa={sensitive:!1,strict:!1,start:!0,end:!0},xa=/[.+*?^${}()[\]/\\]/g;function Sa(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Ca(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=Sa(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(Ta(o))return 1;if(Ta(r))return-1}return r.length-o.length}function Ta(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ea={type:0,value:""},ka=/[a-zA-Z0-9_]/;function Oa(e,t,n){const o=function(e,t){const n=Cs({},wa,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(xa,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){s+=10;try{new RegExp(`(${d})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+a.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=c&&l.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),r+=f,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===d&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(ks(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=ks(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Ea]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function f(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&d(),s()):":"===a?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===a?n=2:ka.test(a)?f():(d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),s(),r}(e.path),n),r=Cs(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Aa(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:La(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Ra(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Cs({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,f;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=Oa(t,n,c),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),a&&e.name&&!Pa(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&s(d)}return f?()=>{i(f)}:Es}function i(e){if(ha(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){let t=0;for(;t<n.length&&Ca(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Ba(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Pa(e)&&o.set(e.record.name,e)}return t=Ra({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw _a(1,{location:e});s=r.record.name,a=Cs($a(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&$a(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw _a(1,{location:e,currentLocation:t});s=r.record.name,a=Cs({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Ma(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function $a(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function La(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Pa(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ma(e){return e.reduce(((e,t)=>Cs(e,t.meta)),{})}function Ra(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Ba(e,t){return t.children.some((t=>t===e||Ba(e,t)))}function Ia(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Ms," "),r=e.indexOf("="),i=qs(r<0?e:e.slice(0,r)),s=r<0?null:qs(e.slice(r+1));if(i in t){let e=t[i];ks(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function ja(e){let t="";for(let n in e){const o=e[n];if(n=Ws(n).replace(Ls,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(ks(o)?o.map((e=>e&&Ws(e))):[o&&Ws(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Da(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=ks(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Na=Symbol(""),Fa=Symbol(""),Va=Symbol(""),Ha=Symbol(""),Wa=Symbol("");function za(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function qa(e,t,n,o,r,i=(e=>e())){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(_a(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(_a(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch((e=>l(e)))}))}function Ua(e,t,n,o,r=(e=>e())){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(qa(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&qa(c,n,o,a,e,r)()}))))}}var s;return i}function Ya(e){const t=yr(Va),n=yr(Ha),o=Ti((()=>t.resolve(ln(e.to)))),r=Ti((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(Ks.bind(null,r));if(s>-1)return s;const a=Ka(e[t-2]);return t>1&&Ka(r)===a&&i[i.length-1].path!==a?i.findIndex(Ks.bind(null,e[t-2])):s})),i=Ti((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!ks(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=Ti((()=>r.value>-1&&r.value===n.matched.length-1&&Js(n.params,o.value.params)));return{route:o,href:Ti((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[ln(e.replace)?"replace":"push"](ln(e.to)).catch(Es):Promise.resolve()}}}const Xa=yo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Ya,setup(e,{slots:t}){const n=Ht(Ya(e)),{options:o}=yr(Va),r=Ti((()=>({[Ja(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Ja(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Ei("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function Ka(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ja=(e,t,n)=>null!=e?e:null!=t?t:n;function Ga(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Qa=yo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=yr(Wa),r=Ti((()=>e.route||o.value)),i=yr(Fa,0),s=Ti((()=>{let e=ln(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=Ti((()=>r.value.matched[s.value]));vr(Fa,Ti((()=>s.value+1))),vr(Na,a),vr(Wa,r);const l=rn();return Zn((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Ks(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return Ga(n.default,{Component:c,route:o});const u=s.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,f=Ei(c,Cs({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return Ga(n.default,{Component:f,route:o})||f}}});function Za(e){const t=Aa(e.routes,e),n=e.parseQuery||Ia,o=e.stringifyQuery||ja,r=e.history,i=za(),s=za(),a=za(),l=sn(ga,!0);let c=ga;Ss&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Ts.bind(null,(e=>""+e)),d=Ts.bind(null,zs),f=Ts.bind(null,qs);function p(e,i){if(i=Cs({},i||l.value),"string"==typeof e){const o=Ys(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return Cs(o,s,{params:f(s.params),hash:qs(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=Cs({},e,{path:Ys(n,e.path,i.path).path});else{const t=Cs({},e.params);for(const e in t)null==t[e]&&delete t[e];s=Cs({},e,{params:d(t)}),i.params=d(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(f(a.params));const p=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Cs({},e,{hash:(h=c,Hs(h).replace(Ds,"{").replace(Fs,"}").replace(Is,"^")),path:a.path}));var h;const g=r.createHref(p);return Cs({fullPath:p,hash:c,query:o===ja?Da(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Ys(n,e,l.value.path):Cs({},e)}function g(e,t){if(c!==e)return _a(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),Cs({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=p(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(Cs(h(u),{state:"object"==typeof u?Cs({},i,u.state):i,force:s,replace:a}),t||n);const d=n;let f;return d.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Ks(t.matched[o],n.matched[r])&&Js(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(f=_a(16,{to:d,from:r}),L(r,r,!0,!1)),(f?Promise.resolve(f):w(d,r)).catch((e=>ba(e)?ba(e,2)?e:$(e):A(e,d,r))).then((e=>{if(e){if(ba(e,2))return y(Cs({replace:a},h(e.to),{state:"object"==typeof e.to?Cs({},i,e.to.state):i,force:s}),t||d)}else e=S(d,r,!0,a,i);return x(d,r,e),e}))}function _(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function b(e){const t=R.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>Ks(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>Ks(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=Ua(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(qa(o,e,t))}));const l=_.bind(null,e,t);return n.push(l),I(n).then((()=>{n=[];for(const o of i.list())n.push(qa(o,e,t));return n.push(l),I(n)})).then((()=>{n=Ua(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(qa(o,e,t))}));return n.push(l),I(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if(ks(o.beforeEnter))for(const r of o.beforeEnter)n.push(qa(r,e,t));else n.push(qa(o.beforeEnter,e,t));return n.push(l),I(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ua(a,"beforeRouteEnter",e,t,b),n.push(l),I(n)))).then((()=>{n=[];for(const o of s.list())n.push(qa(o,e,t));return n.push(l),I(n)})).catch((e=>ba(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>b((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=g(e,t);if(s)return s;const a=t===ga,c=Ss?history.state:{};n&&(o||a?r.replace(e.fullPath,Cs({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,L(e,t,n,a),$()}let C;function T(){C||(C=r.listen(((e,t,n)=>{if(!B.listening)return;const o=p(e),i=v(o);if(i)return void y(Cs(i,{replace:!0}),o).catch(Es);c=o;const s=l.value;var a,u;Ss&&(a=la(s.fullPath,n.delta),u=sa(),ca.set(a,u)),w(o,s).catch((e=>ba(e,12)?e:ba(e,2)?(y(e.to,o).then((e=>{ba(e,20)&&!n.delta&&n.type===Zs.pop&&r.go(-1,!1)})).catch(Es),Promise.reject()):(n.delta&&r.go(-n.delta,!1),A(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!ba(e,8)?r.go(-n.delta,!1):n.type===Zs.pop&&ba(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(Es)})))}let E,k=za(),O=za();function A(e,t,n){$(e);const o=O.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function $(e){return E||(E=!e,T(),k.list().forEach((([t,n])=>e?n(e):t())),k.reset()),e}function L(t,n,o,r){const{scrollBehavior:i}=e;if(!Ss||!i)return Promise.resolve();const s=!o&&function(e){const t=ca.get(e);return ca.delete(e),t}(la(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return Cn().then((()=>i(t,n,s))).then((e=>e&&aa(e))).catch((e=>A(e,t,n)))}const P=e=>r.go(e);let M;const R=new Set,B={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return ha(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:p,options:e,push:m,replace:function(e){return m(Cs(h(e),{replace:!0}))},go:P,back:()=>P(-1),forward:()=>P(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:O.add,isReady:function(){return E&&l.value!==ga?Promise.resolve():new Promise(((e,t)=>{k.add([e,t])}))},install(e){e.component("RouterLink",Xa),e.component("RouterView",Qa),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>ln(l)}),Ss&&!M&&l.value===ga&&(M=!0,m(r.location).catch((e=>{})));const t={};for(const o in ga)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Va,this),e.provide(Ha,Wt(t)),e.provide(Wa,l);const n=e.unmount;R.add(e),e.unmount=function(){R.delete(e),R.size<1&&(c=ga,C&&C(),C=null,l.value=ga,M=!1,E=!1),n()}}};function I(e){return e.reduce(((e,t)=>e.then((()=>b(t)))),Promise.resolve())}return B}function el(){return yr(Ha)}const tl=["{","}"];const nl=/^(?:\d)+/,ol=/^(?:\w)+/;const rl=Object.prototype.hasOwnProperty,il=(e,t)=>rl.call(e,t),sl=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=tl){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=nl.test(t)?"list":a&&ol.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function al(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class ll{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||sl,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=al(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{il(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=al(t,this.messages))&&(o=this.messages[t]):n=t,il(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function cl(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Vu?Vu():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new ll({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=wp().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function ul(e,t){return e.indexOf(t[0])>-1}const dl=ie((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let fl;function pl(e){return ul(e,ee)?ml().f(e,function(){const e=Vu(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ee):e}function hl(e,t){if(1===t.length){if(e){const n=e=>v(e)&&ul(e,ee),o=t[0];let r=[];if(p(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return hl(e&&e[n],t)}function gl(e,t){const n=hl(e,t);if(!n)return!1;const o=t[t.length-1];if(p(n))n.forEach((e=>gl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>pl(e),set(t){e=t}})}return!0}function ml(){if(!fl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,fl=cl(e),dl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>fl.add(e,__uniConfig.locales[e]))),fl.setLocale(e)}}return fl}function vl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const yl=ie((()=>{const e="uni.async.",t=["error"];ml().add("en",vl(e,t,["The connection timed out, click the screen to try again."]),!1),ml().add("es",vl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),ml().add("fr",vl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),ml().add("zh-Hans",vl(e,t,["连接服务器超时，点击屏幕重试"]),!1),ml().add("zh-Hant",vl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),_l=ie((()=>{const e="uni.showToast.",t=["unpaired"];ml().add("en",vl(e,t,["Please note showToast must be paired with hideToast"]),!1),ml().add("es",vl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),ml().add("fr",vl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),ml().add("zh-Hans",vl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),ml().add("zh-Hant",vl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),bl=ie((()=>{const e="uni.showLoading.",t=["unpaired"];ml().add("en",vl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),ml().add("es",vl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),ml().add("fr",vl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),ml().add("zh-Hans",vl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),ml().add("zh-Hant",vl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),wl=ie((()=>{const e="uni.showModal.",t=["cancel","confirm"];ml().add("en",vl(e,t,["Cancel","OK"]),!1),ml().add("es",vl(e,t,["Cancelar","OK"]),!1),ml().add("fr",vl(e,t,["Annuler","OK"]),!1),ml().add("zh-Hans",vl(e,t,["取消","确定"]),!1),ml().add("zh-Hant",vl(e,t,["取消","確定"]),!1)})),xl=ie((()=>{const e="uni.picker.",t=["done","cancel"];ml().add("en",vl(e,t,["Done","Cancel"]),!1),ml().add("es",vl(e,t,["OK","Cancelar"]),!1),ml().add("fr",vl(e,t,["OK","Annuler"]),!1),ml().add("zh-Hans",vl(e,t,["完成","取消"]),!1),ml().add("zh-Hant",vl(e,t,["完成","取消"]),!1)}));function Sl(e){const t=new Le;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Cl=1;const Tl=Object.create(null);function El(e,t){return e+"."+t}function kl({id:e,name:t,args:n},o){t=El(o,t);const r=t=>{e&&Kh.publishHandler("invokeViewApi."+e,t)},i=Tl[t];i?i(n,r):r({})}const Ol=c(Sl("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Kh,i=n?Cl++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Al=ye(!0);let $l;function Ll(){$l&&(clearTimeout($l),$l=null)}let Pl=0,Ml=0;function Rl(e){if(Ll(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Pl=t,Ml=n,$l=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Bl(e){if(!$l)return;if(1!==e.touches.length)return Ll();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Pl)>10||Math.abs(n-Ml)>10?Ll():void 0}function Il(e,t){const n=Number(e);return isNaN(n)?t:n}function jl(){const e=__uniConfig.globalStyle||{},t=Il(e.rpxCalcMaxDeviceWidth,960),n=Il(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Dl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Nl,Fl,Vl=["top","left","right","bottom"],Hl={};function Wl(){return Fl="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function zl(){if(Fl="string"==typeof Fl?Fl:Wl()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Vl.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Nl=!0}else Vl.forEach((function(e){Hl[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Fl+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Ul.length||setTimeout((function(){var e={};Ul.forEach((function(t){e[t]=Hl[t]})),Ul.length=0,Yl.forEach((function(t){t(e)}))}),0);Ul.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Hl,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function ql(e){return Nl||zl(),Hl[e]}var Ul=[];var Yl=[];const Xl=Dl({get support(){return 0!=("string"==typeof Fl?Fl:Wl()).length},get top(){return ql("top")},get left(){return ql("left")},get right(){return ql("right")},get bottom(){return ql("bottom")},onChange:function(e){Wl()&&(Nl||zl(),"function"==typeof e&&Yl.push(e))},offChange:function(e){var t=Yl.indexOf(e);t>=0&&Yl.splice(t,1)}}),Kl=_s((()=>{}),["prevent"]),Jl=_s((e=>{}),["stop"]);function Gl(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function Ql(){const e=Gl(document.documentElement.style,"--window-top");return e?e+Xl.top:0}function Zl(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function ec(e){return Zl(e)}function tc(e){return Symbol(e)}function nc(e){return e.$page}function oc(e){return 0===e.tagName.indexOf("UNI-")}const rc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",ic="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",sc="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z";function ac(e,t="#000",n=27){return ni("svg",{width:n,height:n,viewBox:"0 0 32 32"},[ni("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function lc(){const e=Pd(),t=e.length;if(t)return e[t-1]}function cc(){var e;const t=null==(e=lc())?void 0:e.$page;if(t)return t.meta}function uc(){const e=cc();return e?e.id:-1}function dc(){const e=lc();if(e)return e.$vm}const fc=["navigationBar","pullToRefresh"];function pc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);fc.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function hc(e,t,n){if(v(e))n=t,t=e,e=dc();else if("number"==typeof e){const t=Pd().find((t=>nc(t).id===e));e=t?t.$vm:dc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function gc(e){e.preventDefault()}let mc,vc=0;function yc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-vc)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(vc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(mc=setTimeout(s,300))),o=!1};return function(){clearTimeout(mc),o||requestAnimationFrame(s),o=!0}}function _c(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return _c(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),re(i.concat(n).join("/"))}function bc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}function wc(){jl(),ge(oc),window.addEventListener("touchstart",Rl,Al),window.addEventListener("touchmove",Bl,Al),window.addEventListener("touchend",Ll,Al),window.addEventListener("touchcancel",Ll,Al)}class xc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(de(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&de(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Ec(this.$el.querySelector(e));return t?Sc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Ec(n[o]);e&&t.push(Sc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||v(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:$(n);(v(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(v(e)&&(e=W(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];m(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Kh.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Sc(e,t=!0){if(t&&e&&(e=ue(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new xc(e)),e.$el.__wxsComponentDescriptor}function Cc(e,t){return Sc(e,t)}function Tc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Cc(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=ue(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Cc(r,!1)]}}function Ec(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function kc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=_e(t?r:function(e){for(;!oc(e);)e=e.parentElement;return e}(r)),a=_e(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e instanceof CustomEvent&&S(e.detail)&&(l.detail=e.detail),e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Oc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Ac(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const $c=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!oc(o);if(r)return Tc(e,t,n,!1)||[e];const i=kc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=Ql();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Oc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=Ql();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Oc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=Ql();i.touches=Ac(e.touches,t),i.changedTouches=Ac(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Tc(i,t,n)||[i]},createNativeEvent:kc},Symbol.toStringTag,{value:"Module"});function Lc(e){!function(e){const t=e.globalProperties;c(t,$c),t.$gcd=Cc}(e._context.config)}let Pc=1;function Mc(e){return(e||uc())+".invokeViewApi"}const Rc=c(Sl("view"),{invokeOnCallback:(e,t)=>Jh.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Jh,s=o?Pc++:0;o&&r("invokeViewApi."+s,o,!0),i(Mc(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Jh,a=Pc++,l="invokeViewApi."+a;return r(l,n),s(Mc(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Bc(e){hc(lc(),"onResize",e),Jh.invokeOnCallback("onWindowResize",e)}function Ic(e){const t=lc();hc(wp(),"onShow",e),hc(t,"onShow")}function jc(){hc(wp(),"onHide"),hc(lc(),"onHide")}const Dc=["onPageScroll","onReachBottom"];function Nc(){Dc.forEach((e=>Jh.subscribe(e,function(e){return(t,n)=>{hc(parseInt(n),e,t)}}(e))))}function Fc(){!function(){const{on:e}=Jh;e("onResize",Bc),e("onAppEnterForeground",Ic),e("onAppEnterBackground",jc)}(),Nc()}function Vc(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Te(this.$page.id)),e.eventChannel}}function Hc(e){e._context.config.globalProperties.getOpenerEventChannel=Vc}function Wc(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function zc(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${Fu(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function qc(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(zc)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?zc(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const Uc={props:["animation"],watch:{animation:{deep:!0,handler(){qc(this)}}},mounted(){qc(this)}},Yc=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Uc),Xc(e)},Xc=e=>(e.__reserved=!0,e.compatConfig={MODE:3},yo(e));function Kc(e){return e.__wwe=!0,e}function Jc(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=_e(n),{type:t.__evName||o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const Gc={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};const Qc=tc("uf"),Zc=tc("upm");function eu(){return yr(Zc)}function tu(e){const t=function(e){return Ht(function(e){{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:r}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=r||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==Pd().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(pc(el().meta,e)))))}(e);return vr(Zc,t),t}function nu(){return el()}function ou(){return history.state&&history.state.__id__||1}const ru=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function iu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function su(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let au=1;const lu={};function cu(e,t,n){if("number"==typeof e){const o=lu[e];if(o)return o.keepAlive||delete lu[e],o.callback(t,n)}return t}const uu="success",du="fail",fu="complete";function pu(e,t={},{beforeAll:n,beforeSuccess:o}={}){S(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];m(o)&&(t[n]=su(o),delete e[n])}return t}(t),a=m(r),l=m(i),c=m(s),u=au++;return function(e,t,n,o=!1){lu[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),m(n)&&n(u),u.errMsg===e+":ok"?(m(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const hu="success",gu="fail",mu="complete",vu={},yu={};function _u(e,t){return function(n){return e(n,t)||n}}function bu(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(_u(i,n));else{const e=i(t,n);if(b(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function wu(e,t={}){return[hu,gu,mu].forEach((n=>{const o=e[n];if(!p(o))return;const r=t[n];t[n]=function(e){bu(o,e,t).then((e=>m(r)&&r(e)||e))}})),t}function xu(e,t){const n=[];p(vu.returnValue)&&n.push(...vu.returnValue);const o=yu[e];return o&&p(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Su(e){const t=Object.create(null);Object.keys(vu).forEach((e=>{"returnValue"!==e&&(t[e]=vu[e].slice())}));const n=yu[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Cu(e,t,n,o){const r=Su(e);if(r&&Object.keys(r).length){if(p(r.invoke)){return bu(r.invoke,n).then((n=>t(wu(Su(e),n),...o)))}return t(wu(r,n),...o)}return t(n,...o)}function Tu(e,t){return(n={},...o)=>function(e){return!(!S(e)||![uu,du,fu].find((t=>m(e[t]))))}(n)?xu(e,Cu(e,t,c({},n),o)):xu(e,new Promise(((r,i)=>{Cu(e,t,c({},n,{success:r,fail:i}),o)})))}function Eu(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,cu(e,c({errMsg:i},o))}function ku(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(v(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!S(t.formatArgs)&&S(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(m(s)){const o=s(e[0][t],n);if(v(o))return o}else f(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Ou(e,t,n,o){return n=>{const r=pu(e,n,o),i=ku(0,[n],0,o);return i?Eu(r,e,i):t(n,{resolve:t=>function(e,t,n){return cu(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Eu(r,e,function(e){return!e||v(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Au(e,t,n,o){return Tu(e,Ou(e,t,0,o))}function $u(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=ku(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Lu(e,t,n,o){return Tu(e,function(e,t,n,o){return Ou(e,t,0,o)}(e,t,0,o))}let Pu=!1,Mu=0,Ru=0,Bu=960,Iu=375,ju=750;function Du(){let e,t,n;{const{windowWidth:o,pixelRatio:r,platform:i}=function(){const e=of(),t=af(sf(e,rf(e)));return{platform:Qd?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();e=o,t=r,n=i}Mu=e,Ru=t,Pu="ios"===n}function Nu(e,t){const n=Number(e);return isNaN(n)?t:n}const Fu=$u(0,((e,t)=>{if(0===Mu&&(Du(),function(){const e=__uniConfig.globalStyle||{};Bu=Nu(e.rpxCalcMaxDeviceWidth,960),Iu=Nu(e.rpxCalcBaseDeviceWidth,375),ju=Nu(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Mu;n=e===ju||n<=Bu?n:Iu;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Ru&&Pu?.5:1),e<0?-o:o})),Vu=$u(0,(()=>{const e=wp();return e&&e.$vm?e.$vm.$locale:ml().getLocale()})),Hu={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const Wu="json",zu=["text","arraybuffer"],qu=encodeURIComponent;ArrayBuffer,Boolean;const Uu={formatArgs:{method(e,t){t.method=iu((e||"").toUpperCase(),ru)},data(e,t){t.data=e||""},url(e,t){t.method===ru[0]&&S(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(f(t,a)){let e=t[a];null==e?e="":S(e)&&(e=JSON.stringify(e)),s[qu(a)]=qu(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==ru[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Wu).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===zu.indexOf(t.responseType)&&(t.responseType="text")}}};const Yu={url:{type:String,required:!0}},Xu=(Gu(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Gu(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),ed("navigateTo")),Ku=ed("reLaunch"),Ju={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(Pd().length-1,e)}}};function Gu(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Qu;function Zu(){Qu=""}function ed(e){return{formatArgs:{url:td(e)},beforeAll:Zu}}function td(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=Pd();return n.length&&(t=nc(n[n.length-1]).route),_c(t,e)}(t)).split("?")[0],r=bc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!v(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(Qu===t&&"appLaunch"!==n.openType)return`${Qu} locked`;__uniConfig.ready&&(Qu=t)}else if(r.meta.isTabBar){const e=Pd(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const nd={formatArgs:{duration:300}},od=(Boolean,{formatArgs:{title:"",mask:!1}}),rd=(Boolean,{beforeInvoke(){wl()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!f(t,"cancelText")){const{t:e}=ml();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!f(t,"confirmText")){const{t:e}=ml();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),id=["success","loading","none","error"],sd=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=iu(e,id)},image(e,t){t.image=e?Kd(e):""},duration:1500,mask:!1}});function ad(){const e=dc();if(!e)return;const t=Ld(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Rd(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,hc(e,"onHide"))}function ld(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function cd(e){const t=Ld().values();for(const n of t){const t=Td(n);if(ld(e,t))return n.$.__isActive=!0,t.id}}const ud=Lu("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(Ed.handledBeforeEntryPageRoutes)return ad(),pd({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},cd(e)).then(o).catch(r);Od.push({args:{type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,ed("switchTab"));function dd(){const e=Ld().keys();for(const t of e)Rd(t)}const fd=Lu("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(Ed.handledBeforeEntryPageRoutes)return dd(),pd({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o);$d.push({args:{type:"reLaunch",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Ku);function pd({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=wp().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Se(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++Bd,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(ba(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Te(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}function hd(){if(Ed.handledBeforeEntryPageRoutes)return;Ed.handledBeforeEntryPageRoutes=!0;const e=[...kd];kd.length=0,e.forEach((({args:e,resolve:t,reject:n})=>pd(e).then(t).catch(n)));const t=[...Od];Od.length=0,t.forEach((({args:e,resolve:t,reject:n})=>(ad(),pd(e,cd(e.url)).then(t).catch(n))));const n=[...Ad];Ad.length=0,n.forEach((({args:e,resolve:t,reject:n})=>(function(){const e=lc();if(!e)return;const t=Td(e);Rd(Dd(t.path,t.id))}(),pd(e).then(t).catch(n))));const o=[...$d];$d.length=0,o.forEach((({args:e,resolve:t,reject:n})=>(dd(),pd(e).then(t).catch(n))))}let gd;function md(){var e;return gd||(gd=__uniConfig.tabBar&&Ht((e=__uniConfig.tabBar,dl()&&e.list&&e.list.forEach((e=>{gl(e,["text"])})),e))),gd}function vd(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const yd=vd("top:env(a)"),_d=vd("top:constant(a)"),bd=vd("backdrop-filter:blur(10px)"),wd=(()=>yd?"env":_d?"constant":"")();function xd(e){let t=0,n=0;if("custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),e.isTabBar){const e=md();e.shown&&(n=parseInt(e.height))}var o;ec({"--window-top":(o=t,wd?`calc(${o}px + ${wd}(safe-area-inset-top))`:`${o}px`),"--window-bottom":Sd(n)})}function Sd(e){return wd?`calc(${e}px + ${wd}(safe-area-inset-bottom))`:`${e}px`}const Cd=new Map;function Td(e){return e.$page}const Ed={handledBeforeEntryPageRoutes:!1},kd=[],Od=[],Ad=[],$d=[];function Ld(){return Cd}function Pd(){return Md()}function Md(){const e=[],t=Cd.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Rd(e,t=!0){const n=Cd.get(e);n.$.__isUnload=!0,hc(n,"onUnload"),Cd.delete(e),t&&function(e){const t=Nd.get(e);t&&(Nd.delete(e),Fd.pruneCacheEntry(t))}(e)}let Bd=ou();function Id(e){const t=eu();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=Re(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:re(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function jd(e){const t=Id(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Cd.set(Dd(t.path,t.id),e),1===Cd.size&&setTimeout((()=>{hd()}),0)}function Dd(e,t){return e+"$$"+t}const Nd=new Map,Fd={get:e=>Nd.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;Fd.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;Fd.delete(n),Fd.pruneCacheEntry(e),Cn((()=>{Cd.forEach(((e,t)=>{e.$.isUnmounted&&Cd.delete(t)}))}))}}))}(e),Nd.set(e,t)},delete(e){Nd.get(e)&&Nd.delete(e)},forEach(e){Nd.forEach(e)}};function Vd(e,t){!function(e){const t=Wd(e),{body:n}=document;zd&&n.removeAttribute(zd),t&&n.setAttribute(t,""),zd=t}(e),xd(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),Yd(e,t)}function Hd(e){const t=Wd(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Wd(e){return e.type.__scopeId}let zd;const qd=!!(()=>{let e=!1;try{const t={};Object.defineProperty(t,"passive",{get(){e=!0}}),window.addEventListener("test-passive",(()=>{}),t)}catch(t){}return e})()&&{passive:!1};let Ud;function Yd(e,t){if(document.removeEventListener("touchmove",gc),Ud&&document.removeEventListener("scroll",Ud),t.disableScroll)return document.addEventListener("touchmove",gc,qd);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=Td(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Kh.publishHandler("onPageScroll",{scrollTop:o},e),n&&Kh.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Kh.publishHandler("onReachBottom",{},s)),Ud=yc(i),requestAnimationFrame((()=>document.addEventListener("scroll",Ud)))}function Xd(e){const{base:t}=__uniConfig.router;return 0===re(e).indexOf(t)?re(e):t+e}function Kd(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Xd(e.slice(1));e="https:"+e}if(te.test(e)||ne.test(e)||0===e.indexOf("blob:"))return e;const o=Md();return o.length?Xd(_c(Td(o[o.length-1]).route,e).slice(1)):e}const Jd=navigator.userAgent,Gd=/android/i.test(Jd),Qd=/iphone|ipad|ipod/i.test(Jd),Zd=Jd.match(/Windows NT ([\d|\d.\d]*)/i),ef=/Macintosh|Mac/i.test(Jd),tf=/Linux|X11/i.test(Jd),nf=ef&&navigator.maxTouchPoints>0;function of(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function rf(e){return e&&90===Math.abs(window.orientation)}function sf(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function af(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}const lf=Wc(),cf=Wc();const uf=Yc({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=rn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Ht({width:-1,height:-1});return Zn((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){ko(o),Do((()=>{t.initial&&Cn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>ni("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[ni("div",{onScroll:r},[ni("div",null,null)],40,["onScroll"]),ni("div",{onScroll:r},[ni("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function df(){}const ff={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function pf(e,t,n){function o(e){const t=Ti((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",df,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",df,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}Zn((()=>t.value),(e=>e&&o(e)))}const hf={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},gf={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},mf={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},vf=Yc({name:"Image",props:hf,setup(e,{emit:t}){const n=rn(null),o=function(e,t){const n=rn(""),o=Ti((()=>{let e="auto",o="";const r=mf[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Ht({rootEl:e,src:Ti((()=>t.src?Kd(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Do((()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0})),r}(n,e),r=Jc(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=gf[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){yf&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Zn((()=>t.mode),((e,t)=>{gf[t]&&r(),gf[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;a(u,d,l),Cn((()=>{o()})),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Zn((()=>e.src),(e=>l(e))),Zn((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),Do((()=>l(e.src))),Vo((()=>c()))}(o,e,n,i,r),()=>ni("uni-image",{ref:n},[ni("div",{style:o.modeStyle},null,4),gf[e.mode]?ni(uf,{onResize:i},null,8,["onResize"]):ni("span",null,null)],512)}});const yf="Google Inc."===navigator.vendor;const _f=ye(!0),bf=[];let wf=0,xf=!1;const Sf=e=>bf.forEach((t=>t.userAction=e));function Cf(){const e=Ht({userAction:!1});return Do((()=>{!function(e={userAction:!1}){xf||(["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!wf&&Sf(!0),wf++,setTimeout((()=>{!--wf&&Sf(!1)}),0)}),_f)})),xf=!0);bf.push(e)}(e)})),Vo((()=>{!function(e){const t=bf.indexOf(e);t>=0&&bf.splice(t,1)}(e)})),{state:e}}function Tf(){const e=Ht({attrs:{}});return Do((()=>{let t=hi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function Ef(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}const kf=function(){var e,t,n;e=uc(),n=Ef,t=El(e,t="getSelectedTextRange"),Tl[t]||(Tl[t]=n)};function Of(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const Af=["none","text","decimal","numeric","tel","search","email","url"],$f=c({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Af.indexOf(e)},cursorColor:{type:String,default:""}},ff),Lf=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function Pf(e,t,n,o){let r=null;r=Ce((n=>{t.value=Of(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),Zn((()=>e.modelValue),r),Zn((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return jo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Mf(e,t){Cf();const n=Ti((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Zn((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Do((()=>{n.value&&Cn(o)}))}function Rf(e,t,n,o){kf();const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=rn(null),r=Jc(t,n),i=Ti((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=Ti((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=Ti((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Ti((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=Of(e.modelValue,e.type)||Of(e.value,e.type);const u=Ht({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return Zn((()=>u.focus),(e=>n("update:focus",e))),Zn((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=Pf(e,i,n,s);Mf(e,r),pf(0,r);const{state:l}=Tf();!function(e,t){const n=yr(Qc,!1);if(!n)return;const o=hi(),r={submit(){const n=o.proxy;return[n[e],v(t)?n[t]:t.value]},reset(){v(t)?o.proxy[t]="":t.value=""}};n.addField(r),Vo((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Zn([()=>t.selectionStart,()=>t.selectionEnd],s),Zn((()=>t.cursor),a),Zn((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),m(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Bf=c({},$f,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),If=ie((()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const n=e.match(/Version\/(\S*)\b/);n&&(t=n[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}}));function jf(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&If()&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Df=Yc({name:"Input",props:Bf,emits:["confirm",...Lf],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=Ti((()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=o.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=Ti((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf($(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=rn(null!=t?t.toLocaleString():"");return Zn((()=>e.modelValue),(e=>{n.value=null!=e?e.toLocaleString():""})),Zn((()=>e.value),(e=>{n.value=null!=e?e.toLocaleString():""})),n}return rn("")}(e,i),l={fn:null};const c=rn(null),{fieldRef:u,state:d,scopedAttrsState:f,fixDisabledColor:p,trigger:h}=Rf(e,c,t,((t,n)=>{const o=t.target;if("number"===i.value){if(l.fn&&(o.removeEventListener("blur",l.fn),l.fn=null),o.validity&&!o.validity.valid){if((!a.value||!o.value)&&"-"===t.data||"-"===a.value[0]&&"deleteContentBackward"===t.inputType)return a.value="-",n.value="",l.fn=()=>{a.value=o.value=""},o.addEventListener("blur",l.fn),!1;const e=jf(t,a,n,o,l);return"boolean"==typeof e?e:(a.value=n.value=o.value="-"===a.value?"":a.value,!1)}{const e=jf(t,a,n,o,l);if("boolean"==typeof e)return e;a.value=o.value}const r=n.maxlength;if(r>0&&o.value.length>r){o.value=o.value.slice(0,r),n.value=o.value;return(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==o.value}}}));Zn((()=>d.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())}));const g=["number","digit"],m=Ti((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&p?ni("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):ni("input",{key:"input",ref:u,value:d.value,onInput:e=>{d.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return ni("uni-input",{ref:c},[ni("div",{class:"uni-input-wrapper"},[ro(ni("div",ci(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Yi,!(d.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?ni("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});function Nf(e){const t=[];return p(e)&&e.forEach((e=>{Jr(e)?e.type===Dr?t.push(...Nf(e.children)):t.push(e):p(e)&&t.push(...Nf(e))})),t}const Ff=function(e,t,n,o){e.addEventListener(t,(e=>{m(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Vf,Hf;const Wf=Yc({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return p(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=rn(null),r=rn(null),i=Jc(o,n),s=function(e){const t=Ht([...e.value]),n=Ht({value:t,height:34});return Zn((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),a=rn(null);Do((()=>{const e=a.value;e&&(s.height=e.$el.offsetHeight)}));let l=rn([]),c=rn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==Fr));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return vr("getPickerViewColumn",(function(e){return Ti({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),vr("pickerViewProps",e),vr("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=Nf(e);l.value=t,Cn((()=>{c.value=t}))}return ni("uni-picker-view",{ref:o},[ni(uf,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),ni("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class zf{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function qf(e,t,n){return e>t-n&&e<t+n}function Uf(e,t){return qf(e,0,t)}class Yf{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Uf(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(Uf(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Uf(t,.4)&&(t=0),Uf(o,.4)&&(o=0),o+=this._endPosition),this._solution&&Uf(o-e,.4)&&Uf(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),qf(this.x(),this._endPosition,.4)&&Uf(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Xf{constructor(e,t,n){this._extent=e,this._friction=t||new zf(.01),this._spring=n||new Yf(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class Kf{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Xf(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(m(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const Jf=Yc({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=rn(null),r=rn(null),i=yr("getPickerViewColumn"),s=hi(),a=i?i(s):rn(0),l=yr("pickerViewProps"),c=yr("pickerViewState"),u=rn(34),d=rn(null);Do((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const f=Ti((()=>(c.height-u.value)/2)),{state:p}=Tf();let h;const g=Ht({current:a.value,length:0});let m;function v(){h&&!m&&(m=!0,Cn((()=>{m=!1;let e=Math.min(g.current,g.length-1);e=Math.max(e,0),h.update(e*u.value,void 0,u.value)})))}Zn((()=>a.value),(e=>{e!==g.current&&(g.current=e,v())})),Zn((()=>g.current),(e=>a.value=e)),Zn([()=>u.value,()=>g.length,()=>c.height],v);let y=0;function _(e){const t=y+e.deltaY;if(Math.abs(t)>10){y=0;let e=Math.min(g.current+(t<0?-1:1),g.length-1);g.current=e=Math.max(e,0),h.scrollTo(e*u.value)}else y=t;e.preventDefault()}function b({clientY:e}){const t=o.value;if(!h.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(g.current+t,g.length-1);g.current=r=Math.max(r,0),h.scrollTo(r*u.value)}}}const w=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new Kf(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new zf(1e-4),spring:new Yf(2,90,20),onSnap:e=>{isNaN(e)||e===g.current||(g.current=e)}});h=n,function(e,t,n){Vo((()=>{document.removeEventListener("mousemove",Vf),document.removeEventListener("mouseup",Hf)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Ff(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),Ff(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),Ff(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const d=Vf=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",d),Ff(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const f=Hf=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),Ff(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),v()};return Do(w),()=>{const e=t.default&&t.default();g.length=Nf(e).length;const n=`${f.value}px 0`;return ni("uni-picker-view-column",{ref:o},[ni("div",{onWheel:_,onClick:b,class:"uni-picker-view-group"},[ni("div",ci(p.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${f.value}px;${l.maskStyle}`}),null,16),ni("div",ci(p.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[ni(uf,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),ni("div",{ref:r,class:["uni-picker-view-content"],style:{padding:n,"--picker-view-column-indicator-height":`${u.value}px`}},[e],4)],40,["onWheel","onClick"])],512)}}}),Gf={ensp:" ",emsp:" ",nbsp:" "};function Qf(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&Gf[t]&&" "===i&&(i=Gf[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,Gf.nbsp).replace(/&ensp;/g,Gf.ensp).replace(/&emsp;/g,Gf.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const Zf=Yc({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=rn(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Fr){const n=Qf(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(ri(e)),t!==r&&o.push(ni("br"))}))}else o.push(t)})),ni("uni-text",{ref:n,selectable:!!e.selectable||null},[ni("span",null,o)],8,["selectable"])}}}),ep=Yc({name:"View",props:c({},Gc),setup(e,{slots:t}){const n=rn(null),{hovering:o,binding:r}=function(e){const t=rn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:Kc((function(e){e.touches.length>1||s(e)})),onMousedown:Kc((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:Kc((function(){a()})),onMouseup:Kc((function(){r&&l()})),onTouchcancel:Kc((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?ni("uni-view",ci({class:o.value?i:"",ref:n},r),[Xo(t,"default")],16):ni("uni-view",{ref:n},[Xo(t,"default")],512)}}});function tp(e,t,n,o){m(t)&&Bo(e,t.bind(n),o)}function np(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&("page"!==o||"component"!==t.renderer)&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!m(t))&&(ke.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];p(r)?r.forEach((e=>tp(o,e,n,t))):tp(o,r,n,t)}})),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,hc(n,"onLoad",e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&hc(n,"onShow")}catch(r){console.error(r.message+"\n"+r.stack)}}}function op(e,t,n){np(e,t,n)}function rp(e,t,n){return e[t]=n}function ip(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function sp(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?hc(i.proxy,"onError",n):hn(n,0,o&&o.$.vnode,!1)}}function ap(e,t){return e?[...new Set([].concat(e,t))]:t}function lp(e){const t=e.config;var n;t.errorHandler=Ae(e,sp),n=t.optionMergeStrategies,ke.forEach((e=>{n[e]=ap}));const o=t.globalProperties;o.$set=rp,o.$applyOptions=op,o.$callMethod=ip,function(e){Oe.forEach((t=>t(e)))}(e)}function cp(e){const t=Za({history:fp(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:dp});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(up[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let up=Object.create(null);const dp=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,up[o]);if(t)return t}return{left:0,top:0};var o};function fp(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),pa(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=Md(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=Td(t[r]);Rd(Dd(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const pp={install(e){lp(e),Lc(e),Hc(e),e.config.warnHandler||(e.config.warnHandler=hp),cp(e)}};function hp(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const gp={class:"uni-async-loading"},mp=ni("i",{class:"uni-loading"},null,-1),vp=Xc({name:"AsyncLoading",render:()=>(zr(),Kr("div",gp,[mp]))});function yp(){window.location.reload()}const _p=Xc({name:"AsyncError",props:["error"],setup(){yl();const{t:e}=ml();return()=>ni("div",{class:"uni-async-error",onClick:yp},[e("uni.async.error")],8,["onClick"])}});let bp;function wp(){return bp}function xp(e){bp=e,Object.defineProperty(bp.$.ctx,"$children",{get:()=>Md().map((e=>e.$vm))});const t=bp.$.appContext.app;t.component(vp.name)||t.component(vp.name,vp),t.component(_p.name)||t.component(_p.name,_p),function(e){e.$vm=e,e.$mpType="app";const t=rn(ml().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(bp),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(bp),Fc(),wc()}function Sp(e,{clone:t,init:n,setup:o,before:r}){t&&(e=c({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=hi();if(n(r.proxy),o(r),i)return i(e,t)},e}function Cp(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Sp(e.default,t):Sp(e,t)}function Tp(e){return Cp(e,{clone:!0,init:jd,setup(e){e.$pageInstance=e;const t=nu(),n=we(t.query);e.attrs.__pageQuery=n,Td(e.proxy).options=n,e.proxy.options=n;const o=eu();var r,i;return xd(o),e.onReachBottom=Ht([]),e.onPageScroll=Ht([]),Zn([e.onReachBottom,e.onPageScroll],(()=>{const t=lc();e.proxy===t&&Yd(e,o)}),{once:!0}),jo((()=>{Vd(e,o)})),Do((()=>{Hd(e);const{onReady:n}=e;n&&R(n),Ap(t)})),Ao((()=>{if(!e.__isVisible){Vd(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&R(n),Cn((()=>{Ap(t)}))}}),"ba",r),function(e,t){Ao(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&R(t)}}})),i=o.id,Kh.subscribe(El(i,"invokeViewApi"),kl),Vo((()=>{!function(e){Kh.unsubscribe(El(e,"invokeViewApi")),Object.keys(Tl).forEach((t=>{0===t.indexOf(e+".")&&delete Tl[t]}))}(o.id)})),n}})}function Ep(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=Vp(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Jh.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function kp(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Jh.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function Op(){const{emit:e}=Jh;"visible"===document.visibilityState?e("onAppEnterForeground",c({},cf)):e("onAppEnterBackground")}function Ap(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&hc("onTabItemTap",{index:n,text:t,pagePath:o})}const $p=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let Lp;function Pp(){if(Lp=Lp||$p.__DC_STAT_UUID,!Lp){Lp=Date.now()+""+Math.floor(1e7*Math.random());try{$p.__DC_STAT_UUID=Lp}catch(e){}}return Lp}function Mp(){if(!0!==__uniConfig.darkmode)return v(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function Rp(){let e,t="0",n="",o="phone";const r=navigator.language;if(Qd){e="iOS";const o=Jd.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=Jd.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(Gd){e="Android";const o=Jd.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=Jd.match(/\((.+?)\)/),i=r?r[1].split(";"):Jd.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(nf){if(n="iPad",e="iOS",o="pad",t=m(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=Jd.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(Zd||ef||tf){n="PC",e="PC",o="pc",t="0";let r=Jd.match(/\((.+?)\)/)[1];if(Zd){switch(e="Windows",Zd[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(ef){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(tf){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(Jd)&&(a=t[n],l=Jd.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLowerCase(),browserVersion:l,language:r,deviceType:o,ua:Jd,osname:e,osversion:t,theme:Mp()}}const Bp=$u(0,(()=>{const e=window.devicePixelRatio,t=of(),n=rf(t),o=sf(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=af(o);let s=window.innerHeight;const a=Xl.top,l={left:Xl.left,right:i-Xl.right,top:Xl.top,bottom:s-Xl.bottom,width:i-Xl.left-Xl.right,height:s-Xl.top-Xl.bottom},{top:c,bottom:u}=function(){const e=document.documentElement.style,t=Ql(),n=Gl(e,"--window-bottom"),o=Gl(e,"--window-left"),r=Gl(e,"--window-right"),i=Gl(e,"--top-window-height");return{top:t,bottom:n?n+Xl.bottom:0,left:o?o+Xl.left:0,right:r?r+Xl.right:0,topWindowHeight:i||0}}();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Xl.top,right:Xl.right,bottom:Xl.bottom,left:Xl.left},screenTop:r-s}}));let Ip,jp=!0;function Dp(){jp&&(Ip=Rp())}const Np=$u(0,(()=>{Dp();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:u}=Ip;return c({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:Pp(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLowerCase():void 0,osVersion:u})})),Fp=$u(0,(()=>{Dp();const{theme:e,language:t,browserName:n,browserVersion:o}=Ip;return c({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Vu?Vu():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})})),Vp=$u(0,(()=>{jp=!0,Dp(),jp=!1;const e=Bp(),t=Np(),n=Fp();jp=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=Ip,l=c(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),Hp={esc:["Esc","Escape"],enter:["Enter"]},Wp=Object.keys(Hp);function zp(){const e=rn(""),t=rn(!1),n=n=>{if(t.value)return;const o=Wp.find((e=>-1!==Hp[e].indexOf(n.key)));o&&(e.value=o),Cn((()=>e.value=""))};return Do((()=>{document.addEventListener("keyup",n)})),Vo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const qp=ni("div",{class:"uni-mask"},null,-1);function Up(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),xs(yo({setup:()=>()=>(zr(),Kr(e,t,null,16))}))}function Yp(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function Xp(e,{onEsc:t,onEnter:n}){const o=rn(e.visible),{key:r,disable:i}=zp();return Zn((()=>e.visible),(e=>o.value=e)),Zn((()=>o.value),(e=>i.value=!e)),Gn((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}const Kp=Au("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,enableChunked:s,withCredentials:a,timeout:l=__uniConfig.networkTimeout.request},{resolve:c,reject:u})=>{let d=null;const p=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(v(t)||t instanceof ArrayBuffer)d=t;else if("json"===p)try{d=JSON.stringify(t)}catch(g){d=t.toString()}else if("urlencoded"===p){const e=[];for(const n in t)f(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));d=e.join("&")}else d=t.toString();let h;if(s){if(void 0===typeof window.fetch||void 0===typeof window.AbortController)throw new Error("fetch or AbortController is not supported in this environment");const t=new AbortController,s=t.signal;h=new Gp(t);const f={method:o,headers:n,body:d,signal:s,credentials:a?"include":"same-origin"},p=setTimeout((function(){h.abort(),u("timeout",{errCode:5})}),l);f.signal.addEventListener("abort",(function(){clearTimeout(p),u("abort",{errCode:600003})})),window.fetch(e,f).then((e=>{const t=e.status,n=e.headers,o=e.body,s={};n.forEach(((e,t)=>{s[t]=e}));const a=Jp(s);if(h._emitter.emit("headersReceived",{header:s,statusCode:t,cookies:a}),!o)return void c({data:"",statusCode:t,header:s,cookies:a});const l=o.getReader(),u=[],d=()=>{l.read().then((({done:e,value:n})=>{if(e){const e=function(e){const t=e.reduce(((e,t)=>e+t.byteLength),0),n=new Uint8Array(t);let o=0;for(const r of e)n.set(new Uint8Array(r),o),o+=r.byteLength;return n.buffer}(u);let n="text"===i?(new TextDecoder).decode(e):e;return"text"===i&&(n=Zp(n,i,r)),void c({data:n,statusCode:t,header:s,cookies:a})}const o=n;u.push(o),h._emitter.emit("chunkReceived",{data:o}),d()}))};d()}),(e=>{u(e,{errCode:5})}))}else{const t=new XMLHttpRequest;h=new Gp(t),t.open(o,e);for(const e in n)f(n,e)&&t.setRequestHeader(e,n[e]);const s=setTimeout((function(){t.onload=t.onabort=t.onerror=null,h.abort(),u("timeout",{errCode:5})}),l);t.responseType=i,t.onload=function(){clearTimeout(s);const e=t.status;let n="text"===i?t.responseText:t.response;"text"===i&&(n=Zp(n,i,r)),c({data:n,statusCode:e,header:Qp(t.getAllResponseHeaders()),cookies:[]})},t.onabort=function(){clearTimeout(s),u("abort",{errCode:600003})},t.onerror=function(){clearTimeout(s),u(void 0,{errCode:5})},t.withCredentials=a,t.send(d)}return h}),0,Uu),Jp=e=>{let t=e["Set-Cookie"]||e["set-cookie"],n=[];if(!t)return[];"["===t[0]&&"]"===t[t.length-1]&&(t=t.slice(1,-1));const o=t.split(";");for(let r=0;r<o.length;r++)-1!==o[r].indexOf("Expires=")||-1!==o[r].indexOf("expires=")?n.push(o[r].replace(",","")):n.push(o[r]);return n=n.join(";").split(","),n};class Gp{constructor(e){this._requestOnChunkReceiveCallbackId=0,this._requestOnChunkReceiveCallbacks=new Map,this._requestOnHeadersReceiveCallbackId=0,this._requestOnHeadersReceiveCallbacks=new Map,this._emitter=new Le,this._controller=e}abort(){this._controller&&(this._controller.abort(),delete this._controller)}onHeadersReceived(e){return this._emitter.on("headersReceived",e),this._requestOnHeadersReceiveCallbackId++,this._requestOnHeadersReceiveCallbacks.set(this._requestOnHeadersReceiveCallbackId,e),this._requestOnHeadersReceiveCallbackId}offHeadersReceived(e){if(null==e)return void this._emitter.off("headersReceived");if("function"==typeof e)return void this._requestOnHeadersReceiveCallbacks.forEach(((t,n)=>{t===e&&(this._requestOnHeadersReceiveCallbacks.delete(n),this._emitter.off("headersReceived",e))}));const t=this._requestOnHeadersReceiveCallbacks.get(e);t&&(this._requestOnHeadersReceiveCallbacks.delete(e),this._emitter.off("headersReceived",t))}onChunkReceived(e){return this._emitter.on("chunkReceived",e),this._requestOnChunkReceiveCallbackId++,this._requestOnChunkReceiveCallbacks.set(this._requestOnChunkReceiveCallbackId,e),this._requestOnChunkReceiveCallbackId}offChunkReceived(e){if(null==e)return void this._emitter.off("chunkReceived");if("function"==typeof e)return void this._requestOnChunkReceiveCallbacks.forEach(((t,n)=>{t===e&&(this._requestOnChunkReceiveCallbacks.delete(n),this._emitter.off("chunkReceived",e))}));const t=this._requestOnChunkReceiveCallbacks.get(e);t&&(this._requestOnChunkReceiveCallbacks.delete(e),this._emitter.off("chunkReceived",t))}}function Qp(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}function Zp(e,t,n){let o=e;if("text"===t&&"json"===n)try{o=JSON.parse(o)}catch(r){}return o}const eh=Lu("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===hc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(wp().$router.go(-e.delta),t()):n("onBackPress")}),0,Ju),th=Lu("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(Ed.handledBeforeEntryPageRoutes)return pd({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);kd.push({args:{type:"navigateTo",url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Xu);function nh(e){__uniConfig.darkmode&&Jh.on("onThemeChange",e)}function oh(e){Jh.off("onThemeChange",e)}function rh(e){let t={};return __uniConfig.darkmode&&(t=Re(e,__uniConfig.themeConfig,Mp())),__uniConfig.darkmode?t:e}function ih(e,t){const n=Ut(e),o=n?Ht(rh(e)):rh(e);return __uniConfig.darkmode&&n&&Zn(e,(e=>{const t=rh(e);for(const n in t)o[n]=t[n]})),t&&nh(t),o}const sh={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},ah=yo({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=rn(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=Xp(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=rn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=sh[e].cancelColor})(e,t)};return Gn((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===Mp()&&n({theme:"dark"}),nh(n))):oh(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:f}=e;return n.value=o,ni(Mi,{name:"uni-fade"},{default:()=>[ro(ni("uni-modal",{onTouchmove:Kl},[qp,ni("div",{class:"uni-modal"},[t?ni("div",{class:"uni-modal__hd"},[ni("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,d?ni("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:f,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):ni("div",{class:"uni-modal__bd",onTouchmovePassive:Jl,textContent:o},null,40,["onTouchmovePassive","textContent"]),ni("div",{class:"uni-modal__ft"},[l&&ni("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),ni("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Yi,s.value]])]})}}});let lh;const ch=ie((()=>{Jh.on("onHidePopup",(()=>lh.visible=!1))}));let uh;function dh(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&lh.editable&&(o.content=t),uh&&uh(o)}const fh=Lu("showModal",((e,{resolve:t})=>{ch(),uh=t,lh?(c(lh,e),lh.visible=!0):(lh=Ht(e),Cn((()=>(Up(ah,lh,dh).mount(Yp("u-a-m")),Cn((()=>lh.visible=!0))))))}),0,rd),ph={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==id.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},hh={light:"#fff",dark:"rgba(255,255,255,0.9)"},gh=e=>hh[e],mh=yo({name:"Toast",props:ph,setup(e){_l(),bl();const{Icon:t}=function(e){const t=rn(gh(Mp())),n=({theme:e})=>t.value=gh(e);Gn((()=>{e.visible?nh(n):oh(n)}));return{Icon:Ti((()=>{switch(e.icon){case"success":return ni(ac(rc,t.value,38),{class:"uni-toast__icon"});case"error":return ni(ac(ic,t.value,38),{class:"uni-toast__icon"});case"loading":return ni("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=Xp(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return ni(Mi,{name:"uni-fade"},{default:()=>[ro(ni("uni-toast",{"data-duration":r},[o?ni("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Kl},null,40,["onTouchmove"]):"",s||t.value?ni("div",{class:"uni-toast"},[s?ni("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,ni("p",{class:"uni-toast__content"},[i])]):ni("div",{class:"uni-sample-toast"},[ni("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Yi,n.value]])]})}}});let vh,yh,_h="";const bh=De();function wh(e){vh?c(vh,e):(vh=Ht(c(e,{visible:!1})),Cn((()=>{bh.run((()=>{Zn([()=>vh.visible,()=>vh.duration],(([e,t])=>{if(e){if(yh&&clearTimeout(yh),"onShowLoading"===_h)return;yh=setTimeout((()=>{Eh("onHideToast")}),t)}else yh&&clearTimeout(yh)}))})),Jh.on("onHidePopup",(()=>Eh("onHidePopup"))),Up(mh,vh,(()=>{})).mount(Yp("u-a-t"))}))),setTimeout((()=>{vh.visible=!0}),10)}const xh=Lu("showToast",((e,{resolve:t,reject:n})=>{wh(e),_h="onShowToast",t()}),0,sd),Sh={icon:"loading",duration:1e8,image:""},Ch=Lu("showLoading",((e,{resolve:t,reject:n})=>{c(e,Sh),wh(e),_h="onShowLoading",t()}),0,od),Th=Lu("hideLoading",((e,{resolve:t,reject:n})=>{Eh("onHideLoading"),t()}));function Eh(e){const{t:t}=ml();if(!_h)return;let n="";if("onHideToast"===e&&"onShowToast"!==_h?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==_h&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);_h="",setTimeout((()=>{vh.visible=!1}),10)}const kh=Lu("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:s,featureSettings:a}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),s&&i.push(`font-variant:${s}`),a&&i.push(`font-feature-settings:${a}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${Kd(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${Kd(t.substring(4,t.length-1))}')`:Kd(t),n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function Oh(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Jh.emit("onNavigationBarChange",{titleText:t})}Gn(t),ko(t)}const Ah=Lu("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(v(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const s=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),s(t-10)}))};s(t)}(t||e||0,n),o()}),0,nd),$h=Lu("stopPullDownRefresh",((e,{resolve:t})=>{Jh.invokeViewMethod("stopPullDownRefresh",{},uc()),t()})),Lh=Xc({name:"TabBar",setup(){const e=rn([]),t=md(),n=ih(t,(()=>{const e=rh(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,n.midButton=e.midButton,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}rn(c({type:"midButton"},e.midButton)),Gn(n)}(n,e),function(e){Zn((()=>e.shown),(t=>{ec({"--window-bottom":Sd(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Gn((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=re(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?ud({from:"tabBar",url:i,tabBarText:r}):hc("onTabItemTap",{index:n,text:r,pagePath:o})}}(el(),n,e),{style:r,borderStyle:i,placeholderStyle:s}=function(e){const t=Ti((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||bd&&n&&"none"!==n&&(t=Ph[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=Ti((()=>{const{borderStyle:t,borderColor:n}=e;return n&&v(n)?{backgroundColor:n}:{backgroundColor:Mh[t]||Mh.black}})),o=Ti((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return Do((()=>{n.iconfontSrc&&kh({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,s)=>{const a=o===s;return function(e,t,n,o,r,i,s,a){return ni("div",{key:s,class:"uni-tabbar__item",onClick:a(r,s)},[Rh(e,t||"",n,o,r,i)],8,["onClick"])}(a?r:i,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,s,t)}))}(n,o,e);return ni("uni-tabbar",{class:"uni-tabbar-"+n.position},[ni("div",{class:"uni-tabbar",style:r.value},[ni("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),ni("div",{class:"uni-placeholder",style:s.value},null,4)],2)}}});const Ph={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},Mh={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Rh(e,t,n,o,r,i){const{height:s}=i;return ni("div",{class:"uni-tabbar__bd",style:{height:s}},[n?Ih(n,o||"rgb(0, 0, 0, 0.8)",r,i):t&&Bh(t,r,i),r.text&&jh(e,r,i),r.redDot&&Dh(r.badge)],4)}function Bh(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return ni("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&ni("img",{src:Kd(e)},null,8,["src"])],6)}function Ih(e,t,n,o){var r;const{type:i,text:s}=n,{iconWidth:a}=o,l="uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||a,color:t};return ni("div",{class:l,style:c},["midButton"!==i&&ni("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function jh(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:s}=n;return ni("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?s:"inherit"}},[r],4)}function Dh(e){return ni("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const Nh=Xc({name:"Layout",setup(e,{emit:t}){const n=rn(null);Zl({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=el();return{routeKey:Ti((()=>Dd("/"+e.meta.route,ou()))),isTabBar:Ti((()=>e.meta.isTabBar)),routeCache:Fd}}(),{layoutState:r,windowState:i}=function(){nu();{const e=Ht({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Zn((()=>e.marginWidth),(e=>Zl({"--window-margin":e+"px"}))),Zn((()=>e.leftWindowWidth+e.marginWidth),(e=>{Zl({"--window-left":e+"px"})})),Zn((()=>e.rightWindowWidth+e.marginWidth),(e=>{Zl({"--window-right":e+"px"})})),{layoutState:e,windowState:Ti((()=>({})))}}}();!function(e,t){const n=nu();function o(){const o=document.body.clientWidth,r=Md();let i={};if(r.length>0){i=Td(r[r.length-1]).meta}else{const e=bc(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((f(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,Cn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,Cn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Zn([()=>n.path],o),Do((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=nu(),n=md(),o=Ti((()=>t.meta.isTabBar&&n.shown));return Zl({"--tab-bar-height":n.height}),o}(),a=function(e){const t=rn(!1);return Ti((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(s);return()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return ni(Qa,null,{default:Nn((({Component:o})=>[(zr(),Kr(To,{matchBy:"key",cache:n},[(zr(),Kr(Un(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return ro(ni(Lh,null,null,512),[[Yi,e.value]])}(s);return ni("uni-app",{ref:n,class:a.value},[e,t],2)}}});function Fh(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!Jr(e)}function Vh(e){if(e.mode===zh.TIME)return"00:00";if(e.mode===zh.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case qh.YEAR:return t.toString();case qh.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Hh(e){if(e.mode===zh.TIME)return"23:59";if(e.mode===zh.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case qh.YEAR:return t.toString();case qh.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function Wh(e,t,n,o){const r=e.mode===zh.DATE?"-":":",i=e.mode===zh.DATE?t.dateArray:t.timeArray;let s;if(e.mode===zh.TIME)s=2;else switch(e.fields){case qh.YEAR:s=1;break;case qh.MONTH:s=2;break;default:s=3}const a=String(n).split(r);let l=[];for(let c=0;c<s;c++){const e=a[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?Wh(e,t,o):l.map((()=>0))),l}const zh={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},qh={YEAR:"year",MONTH:"month",DAY:"day"},Uh={PICKER:"picker",SELECT:"select"},Yh=Yc({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:zh.SELECTOR,validator:e=>Object.values(zh).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>Vh(e)},end:{type:String,default:e=>Hh(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){xl();const{t:o}=ml(),r=rn(null),i=rn(null),s=rn(null),a=rn(null),l=rn(!1),{state:u,rangeArray:d}=function(e){const t=Ht({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=Ti((()=>{let n=e.range;switch(e.mode){case zh.SELECTOR:return[n];case zh.MULTISELECTOR:return n;case zh.TIME:return t.timeArray;case zh.DATE:{const n=t.dateArray;switch(e.fields){case qh.YEAR:return[n[0]];case qh.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),f=Jc(r,t),{system:h,selectorTypeComputed:g,_show:m,_l10nColumn:y,_l10nItem:_,_input:b,_fixInputPosition:w,_pickerViewChange:x,_cancel:S,_change:C,_resetFormData:T,_getFormData:E,_createTime:k,_createDate:O,_setValueSync:A}=function(e,t,n,o,r,i,s){const a=function(){const e=rn(!1);return e.value=(()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0)(),e}(),l=function(){const e=rn("");return e.value=(()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""})(),e}(),c=Ti((()=>{const t=e.selectorType;return Object.values(Uh).includes(t)?t:a.value?Uh.PICKER:Uh.SELECT})),u=Ti((()=>e.mode===zh.DATE&&!Object.values(qh).includes(e.fields)&&t.isDesktop?l.value:"")),d=Ti((()=>Wh(e,t,e.start,Vh(e)))),f=Ti((()=>Wh(e,t,e.end,Hh(e))));function h(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const s=i.getBoundingClientRect();t.popover={top:s.top,left:s.left,width:s.width,height:s.height},setTimeout((()=>{t.visible=!0}),20)}function g(){return{value:t.valueSync,key:e.name}}function m(){switch(e.mode){case zh.SELECTOR:t.valueSync=0;break;case zh.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case zh.DATE:case zh.TIME:t.valueSync=""}}function v(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function y(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function _(){let e=[];const n=y();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function b(e){return 60*e[0]+e[1]}function w(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function x(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function S(){let n=e.value;switch(e.mode){case zh.MULTISELECTOR:{p(n)||(n=t.valueArray),p(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),s=isNaN(o)?isNaN(i)?0:i:o,a=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,s<0||s>a?0:s)}}break;case zh.TIME:case zh.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function C(){let n,o=t.valueSync;switch(e.mode){case zh.MULTISELECTOR:n=[...o];break;case zh.TIME:n=Wh(e,t,o,ae({mode:zh.TIME}));break;case zh.DATE:n=Wh(e,t,o,ae({mode:zh.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function T(){let n=t.valueArray;switch(e.mode){case zh.SELECTOR:return n[0];case zh.MULTISELECTOR:return n.map((e=>e));case zh.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case zh.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function E(){O(),t.valueChangeSource="click";const e=T();t.valueSync=p(e)?e.map((e=>e)):e,n("change",{},{value:e})}function k(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:s,pageY:a}=e;if(s>o&&s<o+r&&a>n&&a<n+i)return}O(),n("cancel",{},{})}function O(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function A(){e.mode===zh.SELECTOR&&c.value===Uh.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function $(e){const n=e.target;t.valueSync=n.value,Cn((()=>{E()}))}function L(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;s.value.style.left=e.clientX-t.left-1.5*n+"px",s.value.style.top=e.clientY-t.top-.5*n+"px"}}function P(e){t.valueArray=M(e.detail.value,!0)}function M(t,n){const{getLocale:o}=ml();if(e.mode===zh.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case qh.YEAR:return t;case qh.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function R(t,n){const{getLocale:o}=ml();if(e.mode===zh.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==qh.YEAR&&n===(e.fields===qh.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return Zn((()=>t.visible),(e=>{e?(clearTimeout(Xh),t.contentVisible=e,A()):Xh=setTimeout((()=>{t.contentVisible=e}),300)})),Zn([()=>e.mode,()=>e.value,()=>e.range],S,{deep:!0}),Zn((()=>t.valueSync),C,{deep:!0}),Zn((()=>t.valueArray),(o=>{if(e.mode===zh.TIME||e.mode===zh.DATE){const n=e.mode===zh.TIME?b:w,o=t.valueArray,r=d.value,i=f.value;if(e.mode===zh.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?x(o,r):n(o)>n(i)&&x(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===zh.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:h,_cancel:k,_change:E,_l10nColumn:M,_l10nItem:R,_input:$,_resetFormData:m,_getFormData:g,_createTime:v,_createDate:_,_setValueSync:S,_fixInputPosition:L,_pickerViewChange:P}}(e,u,f,r,i,s,a);!function(e,t,n){const{key:o,disable:r}=zp();Gn((()=>{r.value=!e.visible})),Zn(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(u,S,C),function(e,t){const n=yr(Qc,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),Vo((()=>{n.removeField(o)}))}}(T,E),k(),O(),A();const $=function(e){const t=rn(0),n=rn(0),o=Ti((()=>t.value>=500&&n.value>=500)),r=Ti((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,s=e.popover;function a(e){return Number(e)||0}if(o.value&&s){c(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=a(s.left),t=a(s.width?s.width:300),o=a(s.top),l=a(s.height),u=e+t/2;r.transform="none !important";const d=Math.max(0,u-t/2);r.left=`${d}px`,s.width&&(r.width=`${t}px`);let f=Math.max(12,u-d);f=Math.min(t-12,f),i.left=`${f}px`;const p=n.value/2;o+l-p>p-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return Do((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=Vp();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),Ho((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}(u);return Gn((()=>{u.isDesktop=$.isDesktop.value,u.popupStyle=$.popupStyle.value})),Vo((()=>{i.value&&i.value.remove()})),Do((()=>{l.value=!0})),()=>{let t;const{visible:c,contentVisible:f,valueArray:p,popupStyle:T,valueSync:E}=u,{rangeKey:k,mode:O,start:A,end:$}=e,L=function(e,t){return v(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}(e,"disabled");return ni("uni-picker",ci({ref:r},L,{onClick:Kc(m)}),[l.value?ni("div",{ref:i,class:["uni-picker-container",`uni-${O}-${g.value}`],onWheel:Kl,onTouchmove:Kl},[ni(Mi,{name:"uni-fade"},{default:()=>[ro(ni("div",{class:"uni-mask uni-picker-mask",onClick:Kc(S),onMousemove:w},null,40,["onClick","onMousemove"]),[[Yi,c]])]}),h.value?null:ni("div",{class:[{"uni-picker-toggle":c},"uni-picker-custom"],style:T.content},[ni("div",{class:"uni-picker-header",onClick:Jl},[ni("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:Kc(S)},[o("uni.picker.cancel")],8,["onClick"]),ni("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:C},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),f?ni(Wf,{value:y(p),class:"uni-picker-content",onChange:x},Fh(t=Yo(y(d.value),((e,t)=>{let n;return ni(Jf,{key:t},Fh(n=Yo(e,((e,n)=>ni("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[k]||"":_(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,ni("div",{ref:s,class:"uni-picker-select",onWheel:Jl,onTouchmove:Jl},[Yo(d.value[0],((e,t)=>ni("div",{key:t,class:["uni-picker-item",{selected:p[0]===t}],onClick:()=>{p[0]=t,C()}},["object"==typeof e?e[k]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),ni("div",{style:T.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,ni("div",null,[n.default&&n.default()]),h.value?ni("div",{class:"uni-picker-system",onMousemove:Kc(w)},[ni("input",{class:["uni-picker-system_input",h.value],ref:a,value:E,type:O,tabindex:"-1",min:A,max:$,onChange:e=>{b(e),Jl(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});let Xh;const Kh=c(Ol,{publishHandler(e,t,n){Jh.subscribeHandler(e,t,n)}}),Jh=c(Rc,{publishHandler(e,t,n){Kh.subscribeHandler(e,t,n)}}),Gh=Xc({name:"PageHead",setup(){const e=rn(null),t=eu(),n=ih(t.navigationBar,(()=>{const e=rh(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:r}=function(e){const t=Ti((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,r={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(r[`uni-page-head-shadow-${o}`]=!0),r})),n=Ti((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const i=function(e,t){if(!t)return ni("div",{class:"uni-page-head-btn",onClick:Zh},[ac(sc,"transparent"===e.type?"#fff":e.titleColor,26)],8,["onClick"])}(n,t.isQuit),s=n.type||"default",a="transparent"!==s&&"float"!==s&&ni("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return ni("uni-page-head",{"uni-page-head-type":s},[ni("div",{ref:e,class:o.value,style:r.value},[ni("div",{class:"uni-page-head-hd"},[i]),Qh(n),ni("div",{class:"uni-page-head-ft"},[])],6),a],8,["uni-page-head-type"])}}});function Qh(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:r}){return ni("div",{class:"uni-page-head-bd"},[ni("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?ni("i",{class:"uni-loading"},null):r?ni("img",{src:r,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}function Zh(){1===Pd().length?fd({url:"/"}):eh({from:"backbutton",success(){}})}const eg=Xc({name:"PageBody",setup(e,t){const n=rn(null),o=rn(null);return Zn((()=>false.enablePullDownRefresh),(()=>{o.value=null}),{immediate:!0}),()=>ni(Dr,null,[!1,ni("uni-page-wrapper",ci({ref:n},o.value),[ni("uni-page-body",null,[Xo(t.slots,"default")]),null],16)])}}),tg=Xc({name:"Page",setup(e,t){let n=tu(ou());const o=n.navigationBar,r={};return Oh(n),()=>ni("uni-page",{"data-page":n.route,style:r},"custom"!==o.style?[ni(Gh),ng(t),null]:[ng(t),null])}});function ng(e){return zr(),Kr(eg,{key:0},{default:Nn((()=>[Xo(e.slots,"page")])),_:3})}const og={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=Fu;const rg=Object.assign({}),ig=Object.assign;window.__uniConfig=ig({globalStyle:{backgroundColor:"#F5F5F5",navigationBar:{backgroundColor:"#007AFF",titleText:"订单图片管理系统",type:"default",titleColor:"#ffffff"},isNVue:!1},tabBar:{position:"bottom",color:"#7A7E83",selectedColor:"#007AFF",borderStyle:"black",blurEffect:"none",fontSize:"18px",iconWidth:"24px",spacing:"8px",height:"50px",list:[{pagePath:"pages/index/index",text:"首页"},{pagePath:"pages/factory/list",text:"工厂管理"},{pagePath:"pages/image/search",text:"图片检索"}],backgroundColor:"#ffffff",selectedIndex:0,shown:!0},uniIdRouter:{},compilerVersion:"4.75"},{appId:"__UNI__56E311F",appName:"Front",appVersion:"1.0.0",appVersionCode:"100",async:og,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(rg).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return ig(e[n]||(e[n]={}),rg[t].default),e}),{}),router:{mode:"hash",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const sg={delay:og.delay,timeout:og.timeout,suspensible:og.suspensible};og.loading&&(sg.loadingComponent={name:"SystemAsyncLoading",render:()=>ni(zn(og.loading))}),og.error&&(sg.errorComponent={name:"SystemAsyncError",props:["error"],render(){return ni(zn(og.error),{error:this.error})}});const ag=()=>t((()=>import("./pages-index-index.D_xV0tTS.js")),__vite__mapDeps([0,1,2])).then((e=>Tp(e.default||e))),lg=bo(ig({loader:ag},sg)),cg=()=>t((()=>import("./pages-factory-list.CclG1KZ5.js")),__vite__mapDeps([3,4,5,6,1,7])).then((e=>Tp(e.default||e))),ug=bo(ig({loader:cg},sg)),dg=()=>t((()=>import("./pages-factory-form.CpmpgYiW.js")),__vite__mapDeps([8,4,5,1,9])).then((e=>Tp(e.default||e))),fg=bo(ig({loader:dg},sg)),pg=()=>t((()=>import("./pages-image-search.DkwsEPrs.js")),__vite__mapDeps([10,5,1,6,11])).then((e=>Tp(e.default||e))),hg=bo(ig({loader:pg},sg));function gg(e,t){return zr(),Kr(tg,null,{page:Nn((()=>[ni(e,ig({},t,{ref:"page"}),null,512)])),_:1})}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=wp(),t=e&&e.$route&&e.$route.query||{};return()=>gg(lg,t)}},loader:ag,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{backgroundColor:"#007AFF",titleText:"订单图片管理系统",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/factory/list",component:{setup(){const e=wp(),t=e&&e.$route&&e.$route.query||{};return()=>gg(ug,t)}},loader:cg,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{backgroundColor:"#007AFF",titleText:"工厂管理",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/factory/form",component:{setup(){const e=wp(),t=e&&e.$route&&e.$route.query||{};return()=>gg(fg,t)}},loader:dg,meta:{navigationBar:{backgroundColor:"#007AFF",titleText:"工厂信息",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/image/search",component:{setup(){const e=wp(),t=e&&e.$route&&e.$route.query||{};return()=>gg(hg,t)}},loader:pg,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{backgroundColor:"#007AFF",titleText:"图片检索",type:"default",titleColor:"#ffffff"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const mg={onLaunch:function(){},onShow:function(){},onHide:function(){}};Cp(mg,{init:xp,setup(e){const t=nu(),n=()=>{var n;n=e,Object.keys(Hu).forEach((e=>{Hu[e].forEach((t=>{Bo(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return c(lf,{path:e,query:t}),c(cf,lf),c({},lf)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:we(t.query)});if(o&&R(o,s),r&&R(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};hd(),i&&R(i,e)}};return yr(Va).isReady().then(n),Do((()=>{window.addEventListener("resize",Ce(Ep,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",kp),document.addEventListener("visibilitychange",Op),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Jh.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(zr(),Kr(Nh));e.setup=(e,o)=>{const r=t&&t(e,o);return m(r)?n:r},e.render=n}}),xs(mg).use(pp).mount("#app");export{ro as A,Yi as B,_s as C,vf as D,Kp as E,Dr as F,Df as I,ni as a,ri as b,Kr as c,Zf as d,xh as e,$h as f,fh as g,Xr as h,ep as i,ii as j,Yh as k,pe as l,eh as m,th as n,zr as o,fe as p,Xo as q,Yo as r,ud as s,Y as t,Ah as u,Vp as v,Nn as w,Ch as x,Th as y,zn as z};
