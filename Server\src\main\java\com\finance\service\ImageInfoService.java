package com.finance.service;

import com.finance.common.PageResult;
import com.finance.dto.ImageInfoDTO;
import com.finance.dto.ImageQueryDTO;
import com.finance.entity.ImageInfo;
import com.finance.repository.ImageInfoRepository;
import com.finance.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图片信息服务类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class ImageInfoService {

    @Autowired
    private ImageInfoRepository imageInfoRepository;



    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${finance.server.host:localhost}")
    private String serverHost;

    /**
     * 分页查询图片信息
     */
    public PageResult<ImageInfoDTO> getImagePage(ImageQueryDTO queryDTO) {
        // 创建分页对象
        Pageable pageable = PageRequest.of(
            queryDTO.getCurrent() - 1, // Spring Data页码从0开始
            queryDTO.getSize(),
            Sort.by(Sort.Direction.DESC, "uploadDate", "createdAt")
        );

        // 处理时间范围
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;
        
        if (queryDTO.getStartDate() != null) {
            startDateTime = queryDTO.getStartDate().atStartOfDay();
        }
        
        if (queryDTO.getEndDate() != null) {
            endDateTime = queryDTO.getEndDate().atTime(LocalTime.MAX);
        }

        // 查询数据
        Page<ImageInfo> imagePage = imageInfoRepository.findByConditions(
            queryDTO.getFactoryName(),
            queryDTO.getOrderNumber(),
            queryDTO.getImageName(),
            startDateTime,
            endDateTime,
            pageable
        );

        // 转换为DTO
        List<ImageInfoDTO> imageDTOs = imagePage.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());

        return new PageResult<>(
            imageDTOs,
            imagePage.getTotalElements(),
            queryDTO.getCurrent(),
            queryDTO.getSize()
        );
    }

    /**
     * 根据ID获取图片信息
     */
    public ImageInfoDTO getImageById(Integer id) {
        ImageInfo imageInfo = imageInfoRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("图片信息不存在"));
        return convertToDTO(imageInfo);
    }

    /**
     * 获取所有工厂名称列表
     */
    public List<String> getFactoryNames() {
        return imageInfoRepository.findDistinctFactoryNames();
    }

    /**
     * 根据工厂名称获取订单号列表
     */
    public List<String> getOrderNumbersByFactory(String factoryName) {
        return imageInfoRepository.findDistinctOrderNumbersByFactoryName(factoryName);
    }

    /**
     * 根据工厂名称统计图片数量
     */
    public long countByFactory(String factoryName) {
        return imageInfoRepository.countByFactoryName(factoryName);
    }

    /**
     * 根据订单号统计图片数量
     */
    public long countByOrderNumber(String orderNumber) {
        return imageInfoRepository.countByOrderNumber(orderNumber);
    }

    /**
     * 实体转DTO
     */
    private ImageInfoDTO convertToDTO(ImageInfo imageInfo) {
        ImageInfoDTO dto = new ImageInfoDTO();
        BeanUtils.copyProperties(imageInfo, dto);

        // 格式化文件大小
        dto.setFileSizeFormatted(FileUtil.formatFileSize(imageInfo.getFileSize()));

        // 直接生成图片访问URL（简化版本，提升性能）
        String baseUrl = "http://" + serverHost + ":" + serverPort + "/api/images/";
        String imageUrl = FileUtil.generateImageUrl(imageInfo.getImagePath(), baseUrl);
        dto.setImageUrl(imageUrl);

        // 生成多尺寸URL集合（简化版本）
        Map<String, String> urlSet = new HashMap<>();
        urlSet.put("thumbnail", imageUrl + "?size=small");
        urlSet.put("medium", imageUrl + "?size=medium");
        urlSet.put("large", imageUrl + "?size=large");
        urlSet.put("original", imageUrl);
        dto.setImageUrlSet(urlSet);

        return dto;
    }
}
