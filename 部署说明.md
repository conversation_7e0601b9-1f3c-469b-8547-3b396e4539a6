# 公网服务器部署说明

## 服务器信息
- 服务器IP: ************
- 后端端口: 8084
- 数据库: ************:3306

## 已修改的配置文件

### 1. 后端配置修改

#### application-prod.yml
```yaml
# 生产环境配置
server:
  port: 8084  # 修改为8084端口
  servlet:
    context-path: /api

# 自定义配置
finance:
  # 图片存储路径配置
  image:
    storage-path: /data/uploads/  # 生产环境图片存储路径
    # 图片访问优化配置
    cdn:
      enabled: false  # 是否启用CDN
      domain: ""      # CDN域名
    webp:
      enabled: true   # 是否支持WebP格式
    cache:
      enabled: true   # 是否启用缓存
      max-age: 7200   # 缓存时间（秒）

  # 服务器配置
  server:
    host: ************  # 公网IP
    port: 8084          # 公网端口
```

#### ImageInfoService.java
修改了图片URL生成逻辑，使用配置的服务器地址：
```java
// 生成图片访问URL
String baseUrl = "http://" + serverHost + ":" + serverPort + "/api/images/";
dto.setImageUrl(FileUtil.generateImageUrl(imageInfo.getImagePath(), baseUrl));
```

### 2. 前端配置修改

#### config/api.js
```javascript
// 生产环境配置
const production = {
  baseURL: 'http://************:8084/api',  // 修改为公网地址
  timeout: 10000
}
```

## 部署步骤

### 1. 后端部署
```bash
# 1. 使用生产环境配置打包
mvn clean package -Pprod

# 2. 上传jar包到服务器
scp target/finance-web-server-1.0.0.jar user@************:/opt/finance-web/

# 3. 在服务器上启动应用
java -jar -Dspring.profiles.active=prod finance-web-server-1.0.0.jar

# 4. 或者使用nohup后台运行
nohup java -jar -Dspring.profiles.active=prod finance-web-server-1.0.0.jar > app.log 2>&1 &
```

### 2. 前端部署
```bash
# 1. 设置生产环境
export NODE_ENV=production

# 2. 构建前端项目
npm run build

# 3. 部署到Web服务器（如Nginx）
```

### 3. 服务器环境准备

#### 创建图片存储目录
```bash
sudo mkdir -p /data/uploads
sudo chown -R app_user:app_group /data/uploads
sudo chmod 755 /data/uploads
```

#### 防火墙配置
```bash
# 开放8084端口
sudo firewall-cmd --permanent --add-port=8084/tcp
sudo firewall-cmd --reload
```

## 图片访问验证

部署完成后，图片访问URL格式为：
```
http://************:8084/api/images/工厂名/年-月/日/订单号/图片文件名
```

例如：
```
http://************:8084/api/images/factory1/2024-01/15/ORDER001/image_001.jpg
```

## 注意事项

1. **图片路径映射**: 确保服务器上的图片存储路径 `/data/uploads/` 与数据库中存储的相对路径能正确拼接

2. **跨域配置**: 已配置允许所有来源的跨域访问，如需更严格的安全控制，可修改 `CorsConfig.java`

3. **HTTPS支持**: 如需HTTPS访问，需要配置SSL证书并修改URL为https协议

4. **负载均衡**: 如有多台服务器，需要确保图片存储路径在所有服务器上保持一致

5. **备份策略**: 建议定期备份 `/data/uploads/` 目录下的图片文件

6. **只读系统**: 该系统仅用于图片查看，不包含图片上传功能

7. **图片访问优化**: 系统已集成图片访问优化功能，包括：
   - 多尺寸图片支持（缩略图、中等、大图、原图）
   - WebP格式自动检测和支持
   - 图片URL缓存机制
   - 前端懒加载和预加载
   - CDN支持（可配置启用）

## 故障排查

### 图片无法显示
1. 检查图片文件是否存在于服务器指定路径
2. 检查文件权限是否正确
3. 检查防火墙是否开放8084端口
4. 检查应用日志中的错误信息

### 跨域问题
1. 确认前端配置的API地址正确
2. 检查浏览器控制台是否有CORS错误
3. 验证后端跨域配置是否生效
