<template>
  <view class="lazy-image-container" :style="containerStyle">
    <!-- 加载中占位符 -->
    <view v-if="loading" class="lazy-image-placeholder">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 错误占位符 -->
    <view v-else-if="error" class="lazy-image-error" @click="retry">
      <text class="error-icon">⚠️</text>
      <text class="error-text">加载失败，点击重试</text>
    </view>
    
    <!-- 实际图片 -->
    <image
      v-else
      :src="currentSrc"
      :mode="mode"
      :lazy-load="true"
      :show-loading="false"
      :show-error="false"
      :style="imageStyle"
      @load="onLoad"
      @error="onError"
      class="lazy-image"
    />
  </view>
</template>

<script>
export default {
  name: 'LazyImage',
  props: {
    // 图片源
    src: {
      type: String,
      required: true
    },
    // 多尺寸图片源
    srcSet: {
      type: Object,
      default: () => ({})
    },
    // 图片模式
    mode: {
      type: String,
      default: 'aspectFill'
    },
    // 容器宽度
    width: {
      type: [String, Number],
      default: '100%'
    },
    // 容器高度
    height: {
      type: [String, Number],
      default: '200rpx'
    },
    // 是否启用渐进式加载
    progressive: {
      type: Boolean,
      default: true
    },
    // 预加载阈值（像素）
    threshold: {
      type: Number,
      default: 100
    }
  },
  
  data() {
    return {
      loading: true,
      error: false,
      currentSrc: '',
      observer: null,
      isIntersecting: false
    }
  },
  
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? this.width + 'rpx' : this.width,
        height: typeof this.height === 'number' ? this.height + 'rpx' : this.height,
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#f5f5f5'
      }
    },
    
    imageStyle() {
      return {
        width: '100%',
        height: '100%',
        transition: 'opacity 0.3s ease'
      }
    },
    
    // 简化图片源选择
    optimalSrc() {
      return this.src
    }
  },
  
  mounted() {
    this.initIntersectionObserver()
  },
  
  beforeDestroy() {
    this.destroyIntersectionObserver()
  },
  
  watch: {
    src: {
      handler(newSrc) {
        if (newSrc) {
          this.reset()
          if (this.isIntersecting) {
            this.loadImage()
          }
        }
      },
      immediate: true
    }
  },
  
  methods: {
    /**
     * 初始化交叉观察器
     */
    initIntersectionObserver() {
      // 简化逻辑：直接加载图片，提升性能
      this.isIntersecting = true
      this.loadImage()
    },
    
    /**
     * 销毁交叉观察器
     */
    destroyIntersectionObserver() {
      if (this.observer) {
        this.observer.disconnect()
        this.observer = null
      }
    },
    
    /**
     * 加载图片
     */
    loadImage() {
      if (!this.src || this.currentSrc) return

      this.loading = true
      this.error = false

      // 直接加载图片，简化逻辑提升性能
      this.currentSrc = this.src
    },
    

    
    /**
     * 图片加载成功
     */
    onLoad() {
      this.loading = false
      this.error = false
      this.$emit('load')
    },
    
    /**
     * 图片加载失败
     */
    onError() {
      this.loading = false
      this.error = true
      this.$emit('error')
    },
    
    /**
     * 重试加载
     */
    retry() {
      this.reset()
      this.loadImage()
    },
    
    /**
     * 重置状态
     */
    reset() {
      this.loading = true
      this.error = false
      this.currentSrc = ''
    }
  }
}
</script>

<style scoped>
.lazy-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.lazy-image-placeholder,
.lazy-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.error-text {
  font-size: 24rpx;
  color: #999;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.lazy-image-error {
  cursor: pointer;
}

.lazy-image-error:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.lazy-image {
  border-radius: 8rpx;
}
</style>
