# 生产环境配置
server:
  port: 8084
  servlet:
    context-path: /api
  # 启用压缩
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: finance-web-server
  
  # 数据库配置
  datasource:
    url: ****************************************************************************************************************************************************
    username: mls01
    password: 12345@Mls
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # 生产环境连接池配置
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      auto-commit: true
      idle-timeout: 300000
      pool-name: FinanceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false  # 生产环境关闭SQL日志
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        # 启用查询缓存
        cache:
          use_second_level_cache: true
          use_query_cache: true
        # 批量操作优化
        jdbc:
          batch_size: 20
          order_inserts: true
          order_updates: true
    open-in-view: false

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    # 性能优化
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# 日志配置
logging:
  level:
    com.finance: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/finance-web.log
    max-size: 100MB
    max-history: 30

# 自定义配置
finance:
  # 图片存储路径配置
  image:
    storage-path: /data/uploads/
    # 图片访问优化配置
    cdn:
      enabled: false
      domain: ""
    webp:
      enabled: true
    cache:
      enabled: true
      max-age: 86400  # 24小时

  # 服务器配置
  server:
    host: ************
    port: 8084

  # 分页配置
  page:
    default-size: 20
    max-size: 100

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
