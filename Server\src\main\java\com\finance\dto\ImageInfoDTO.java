package com.finance.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 图片信息数据传输对象
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class ImageInfoDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 图片文件名
     */
    private String imageName;

    /**
     * 图片完整路径
     */
    private String imagePath;

    /**
     * 图片访问URL
     */
    private String imageUrl;

    /**
     * 多尺寸图片URL集合
     * 包含: thumbnail, medium, large, original
     */
    private Map<String, String> imageUrlSet;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件大小(格式化显示)
     */
    private String fileSizeFormatted;

    /**
     * 上传日期
     */
    private LocalDateTime uploadDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
