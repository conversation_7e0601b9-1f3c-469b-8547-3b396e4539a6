import{e as a,f as t,n as e,g as s,c as l,w as r,i as c,o,a as i,b as n,t as u,h as d,r as f,F as h,j as y,I as _,d as g,k as p,l as m}from"./index-DjruG92H.js";import{f as b}from"./factory.o0XEgZaa.js";import{p as k}from"./performance.o1AJnB7v.js";import{r as C}from"./request.j3WTH_xy.js";import{_ as D}from"./_plugin-vue_export-helper.BCo6x5W8.js";const v=D({data:()=>({factoryList:[],pageData:{current:1,size:20,total:0,pages:0},searchQuery:{keyword:"",status:null},statusOptions:[{label:"全部",value:null},{label:"启用",value:1},{label:"禁用",value:0}],statusIndex:0,loading:!1,hasMore:!1,searchTimer:null}),onLoad(){this.loadFactoryList()},onShow(){this.clearFactoryCache(),this.refreshList()},onPullDownRefresh(){this.refreshList()},methods:{async loadFactoryList(e=!1){if(!this.loading){this.loading=!0;try{const a={current:e?this.pageData.current+1:1,size:this.pageData.size,...this.buildSearchParams()},s=await b.getFactoryPageAdmin(a),{data:l}=s;e?(this.factoryList=[...this.factoryList,...l.records],this.pageData.current=l.current):(this.factoryList=l.records,this.pageData=l),this.hasMore=this.pageData.current<this.pageData.pages}catch(s){a({title:"加载失败",icon:"none"})}finally{this.loading=!1,t()}}},buildSearchParams(){const a={};return this.searchQuery.keyword&&(a.factoryName=this.searchQuery.keyword,a.username=this.searchQuery.keyword),null!==this.searchQuery.status&&(a.status=this.searchQuery.status),a},refreshList(){this.clearFactoryCache(),this.pageData.current=1,this.loadFactoryList()},loadMore(){this.hasMore&&!this.loading&&this.loadFactoryList(!0)},onSearchInput:k.debounce((function(){this.handleSearch()}),300),handleSearch(){this.refreshList()},onStatusChange(a){this.statusIndex=a.detail.value,this.searchQuery.status=this.statusOptions[this.statusIndex].value,this.handleSearch()},navigateToAdd(){e({url:"/pages/factory/form"})},navigateToEdit(a){e({url:`/pages/factory/form?id=${a.id}`})},confirmDelete(a){s({title:"确认删除",content:`确定要删除工厂"${a.factoryName}"吗？`,success:t=>{t.confirm&&this.deleteFactory(a.id)}})},async deleteFactory(t){try{await b.deleteFactory(t),this.clearFactoryCache(),this.factoryList=this.factoryList.filter((a=>a.id!==t)),this.pageData.total=Math.max(0,this.pageData.total-1),a({title:"删除成功",icon:"success"}),0===this.factoryList.length&&this.pageData.current>1&&(this.pageData.current=this.pageData.current-1,this.loadFactoryList())}catch(e){a({title:"删除失败",icon:"none"})}},clearFactoryCache(){C.clearCache("/factory"),C.clearCache("/image/factories")},formatDate(a){if(!a)return"";const t=new Date(a);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`}}},[["render",function(a,t,e,s,b,k){const C=_,D=g,v=c,L=p;return o(),l(v,{class:"container"},{default:r((()=>[i(v,{class:"search-section"},{default:r((()=>[i(v,{class:"search-bar"},{default:r((()=>[i(C,{class:"search-input",placeholder:"搜索工厂名称或用户名",modelValue:b.searchQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=a=>b.searchQuery.keyword=a),onInput:k.onSearchInput},null,8,["modelValue","onInput"]),i(v,{class:"search-btn",onClick:k.handleSearch},{default:r((()=>[i(D,{class:"search-icon"},{default:r((()=>[n("🔍")])),_:1})])),_:1},8,["onClick"])])),_:1}),i(v,{class:"filter-bar"},{default:r((()=>[i(L,{value:b.statusIndex,range:b.statusOptions,"range-key":"label",onChange:k.onStatusChange},{default:r((()=>[i(v,{class:"filter-item"},{default:r((()=>[i(D,{class:"filter-label"},{default:r((()=>[n("状态："+u(b.statusOptions[b.statusIndex].label),1)])),_:1}),i(D,{class:"filter-arrow"},{default:r((()=>[n("▼")])),_:1})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1})])),_:1}),i(v,{class:"action-bar"},{default:r((()=>[i(v,{class:"total-info"},{default:r((()=>[i(D,{class:"total-text"},{default:r((()=>[n("共 "+u(b.pageData.total)+" 条记录",1)])),_:1})])),_:1}),i(v,{class:"add-btn",onClick:k.navigateToAdd},{default:r((()=>[i(D,{class:"add-icon"},{default:r((()=>[n("+")])),_:1}),i(D,{class:"add-text"},{default:r((()=>[n("新增工厂")])),_:1})])),_:1},8,["onClick"])])),_:1}),i(v,{class:"factory-list"},{default:r((()=>[(o(!0),d(h,null,f(b.factoryList,(a=>(o(),l(v,{class:"factory-item",key:a.id},{default:r((()=>[i(v,{class:"factory-header"},{default:r((()=>[i(v,{class:"factory-name"},{default:r((()=>[n(u(a.factoryName),1)])),_:2},1024),i(v,{class:m(["factory-status",1===a.status?"status-active":"status-inactive"])},{default:r((()=>[n(u(1===a.status?"启用":"禁用"),1)])),_:2},1032,["class"])])),_:2},1024),i(v,{class:"factory-info"},{default:r((()=>[i(v,{class:"info-row"},{default:r((()=>[i(D,{class:"info-label"},{default:r((()=>[n("用户名：")])),_:1}),i(D,{class:"info-value"},{default:r((()=>[n(u(a.username),1)])),_:2},1024)])),_:2},1024),i(v,{class:"info-row"},{default:r((()=>[i(D,{class:"info-label"},{default:r((()=>[n("密码：")])),_:1}),i(D,{class:"info-value"},{default:r((()=>[n(u(a.password||"未设置"),1)])),_:2},1024)])),_:2},1024),i(v,{class:"info-row"},{default:r((()=>[i(D,{class:"info-label"},{default:r((()=>[n("创建时间：")])),_:1}),i(D,{class:"info-value"},{default:r((()=>[n(u(k.formatDate(a.createdAt)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),i(v,{class:"factory-actions"},{default:r((()=>[i(v,{class:"action-btn edit-btn",onClick:t=>k.navigateToEdit(a)},{default:r((()=>[i(D,{class:"action-icon"},{default:r((()=>[n("✏️")])),_:1}),i(D,{class:"action-text"},{default:r((()=>[n("编辑")])),_:1})])),_:2},1032,["onClick"]),i(v,{class:"action-btn delete-btn",onClick:t=>k.confirmDelete(a)},{default:r((()=>[i(D,{class:"action-icon"},{default:r((()=>[n("🗑️")])),_:1}),i(D,{class:"action-text"},{default:r((()=>[n("删除")])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128))])),_:1}),b.hasMore?(o(),l(v,{key:0,class:"load-more",onClick:k.loadMore},{default:r((()=>[i(D,{class:"load-text"},{default:r((()=>[n(u(b.loading?"加载中...":"加载更多"),1)])),_:1})])),_:1},8,["onClick"])):y("",!0),b.loading||0!==b.factoryList.length?y("",!0):(o(),l(v,{key:1,class:"empty-state"},{default:r((()=>[i(D,{class:"empty-icon"},{default:r((()=>[n("📭")])),_:1}),i(D,{class:"empty-text"},{default:r((()=>[n("暂无工厂数据")])),_:1}),i(v,{class:"empty-btn",onClick:k.navigateToAdd},{default:r((()=>[i(D,{class:"empty-btn-text"},{default:r((()=>[n("立即添加")])),_:1})])),_:1},8,["onClick"])])),_:1}))])),_:1})}],["__scopeId","data-v-128aeba2"]]);export{v as default};
