package com.finance.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 工厂实体类
 * 对应数据库表：Factory_Login
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "Factory_Login")
public class Factory {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    @Column(name = "username", nullable = false, unique = true, length = 50)
    private String username;

    /**
     * 加密密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(max = 255, message = "密码长度不能超过255个字符")
    @Column(name = "password", nullable = false)
    private String password;

    /**
     * 工厂名称
     */
    @NotBlank(message = "工厂名称不能为空")
    @Size(max = 100, message = "工厂名称长度不能超过100个字符")
    @Column(name = "factory_name", nullable = false, length = 100)
    private String factoryName;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 状态：1-启用，0-禁用
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;
}
