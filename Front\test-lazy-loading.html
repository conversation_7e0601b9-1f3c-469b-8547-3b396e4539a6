<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>懒加载测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-item {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .image-wrapper {
            position: relative;
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            overflow: hidden;
        }
        
        .image-thumb {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }
        
        .image-thumb.lazy-loading {
            opacity: 0.6;
            filter: blur(2px);
        }
        
        .loading-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(245, 245, 245, 0.9);
            z-index: 2;
        }
        
        .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .image-info {
            padding: 15px;
        }
        
        .image-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .image-desc {
            color: #666;
            font-size: 14px;
        }
        
        .stats {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .stats h2 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .stats p {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="stats">
            <h2>图片懒加载测试</h2>
            <p>已加载图片: <span id="loaded-count">0</span></p>
            <p>总图片数: <span id="total-count">0</span></p>
            <p>加载进度: <span id="progress">0%</span></p>
        </div>
        
        <div class="image-grid" id="image-grid">
            <!-- 图片将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 模拟图片数据
        const imageData = Array.from({length: 50}, (_, i) => ({
            id: i + 1,
            name: `图片 ${i + 1}`,
            desc: `这是第 ${i + 1} 张测试图片`,
            url: `https://picsum.photos/300/200?random=${i + 1}`
        }));

        let loadedCount = 0;
        const totalCount = imageData.length;
        let intersectionObserver = null;

        // 更新统计信息
        function updateStats() {
            document.getElementById('loaded-count').textContent = loadedCount;
            document.getElementById('total-count').textContent = totalCount;
            document.getElementById('progress').textContent = Math.round((loadedCount / totalCount) * 100) + '%';
        }

        // 创建图片元素
        function createImageElement(data) {
            return `
                <div class="image-item">
                    <div class="image-wrapper">
                        <img 
                            class="image-thumb lazy-loading" 
                            src="/static/images/placeholder.svg"
                            data-src="${data.url}"
                            data-id="${data.id}"
                            alt="${data.name}"
                        />
                        <div class="loading-placeholder">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                    <div class="image-info">
                        <div class="image-name">${data.name}</div>
                        <div class="image-desc">${data.desc}</div>
                    </div>
                </div>
            `;
        }

        // 加载图片
        function loadImage(imgElement, src, imageId) {
            const img = new Image();
            img.onload = () => {
                imgElement.src = src;
                imgElement.classList.remove('lazy-loading');
                const placeholder = imgElement.parentElement.querySelector('.loading-placeholder');
                if (placeholder) {
                    placeholder.style.display = 'none';
                }
                loadedCount++;
                updateStats();
                intersectionObserver.unobserve(imgElement);
            };
            img.onerror = () => {
                console.error('图片加载失败:', src);
                imgElement.classList.remove('lazy-loading');
                const placeholder = imgElement.parentElement.querySelector('.loading-placeholder');
                if (placeholder) {
                    placeholder.style.display = 'none';
                }
                intersectionObserver.unobserve(imgElement);
            };
            img.src = src;
        }

        // 初始化懒加载
        function initLazyLoading() {
            if (!window.IntersectionObserver) {
                console.warn('浏览器不支持 IntersectionObserver');
                return;
            }

            intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const dataSrc = img.getAttribute('data-src');
                        const dataId = img.getAttribute('data-id');
                        
                        if (dataSrc) {
                            loadImage(img, dataSrc, dataId);
                        }
                    }
                });
            }, {
                rootMargin: '50px',
                threshold: 0.1
            });

            // 观察所有图片
            const images = document.querySelectorAll('.image-thumb[data-src]');
            images.forEach(img => {
                intersectionObserver.observe(img);
            });
        }

        // 初始化页面
        function init() {
            const grid = document.getElementById('image-grid');
            grid.innerHTML = imageData.map(createImageElement).join('');
            
            updateStats();
            initLazyLoading();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
