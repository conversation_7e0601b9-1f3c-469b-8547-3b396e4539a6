/**
 * 打印工具类
 * 提供图片打印相关的功能
 */

export default class PrintUtils {
  
  /**
   * 检测可用的打印机
   */
  static async detectPrinters() {
    try {
      // 在Web环境中，我们无法直接检测打印机
      // 返回默认的打印机选项
      return [
        { name: '默认打印机', value: 'default', isDefault: true },
        { name: 'Microsoft Print to PDF', value: 'pdf' },
        { name: 'Microsoft XPS Document Writer', value: 'xps' }
      ]
    } catch (error) {
      return [{ name: '默认打印机', value: 'default', isDefault: true }]
    }
  }

  /**
   * 获取打印机状态
   */
  static async getPrinterStatus(printerName) {
    try {
      // 在Web环境中，我们无法直接获取打印机状态
      // 返回模拟状态
      return {
        name: printerName,
        status: 'ready', // ready, busy, offline, error
        paperLevel: 'normal', // low, normal, high
        inkLevel: 'normal' // low, normal, high
      }
    } catch (error) {
      return {
        name: printerName,
        status: 'unknown',
        paperLevel: 'unknown',
        inkLevel: 'unknown'
      }
    }
  }

  /**
   * 预加载图片
   */
  static preloadImages(imageUrls) {
    return Promise.all(
      imageUrls.map(url => {
        return new Promise((resolve, reject) => {
          const img = new Image()
          img.onload = () => resolve(img)
          img.onerror = () => reject(new Error(`图片加载失败: ${url}`))
          img.src = url
        })
      })
    )
  }

  /**
   * 生成打印样式
   */
  static generatePrintStyles(settings) {
    const { paperSize, quality, imagesPerPage } = settings
    
    return `
      @page {
        size: ${this.getPaperSizeCSS(paperSize.value)};
        margin: 20mm;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }
      
      .print-page {
        page-break-after: always;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: flex-start;
        gap: 10px;
        padding: 10px;
        min-height: calc(100vh - 40mm);
      }
      
      .print-page:last-child {
        page-break-after: avoid;
      }
      
      .image-container {
        flex: 0 0 ${this.getImageContainerWidth(imagesPerPage.value)};
        text-align: center;
        margin-bottom: 15px;
        break-inside: avoid;
      }
      
      .print-image {
        max-width: 100%;
        max-height: ${this.getImageMaxHeight(imagesPerPage.value)};
        object-fit: contain;
        border: 1px solid #ddd;
        border-radius: 4px;
        ${this.getQualityCSS(quality.value)}
      }
      
      .image-info {
        margin-top: 8px;
        font-size: 11px;
        color: #333;
        word-break: break-all;
        line-height: 1.4;
      }
      
      .image-name {
        font-weight: bold;
        margin-bottom: 4px;
        font-size: 12px;
      }
      
      .image-details {
        font-size: 10px;
        color: #666;
        margin-bottom: 2px;
      }
      
      @media print {
        .print-page {
          margin: 0;
          padding: 5mm;
        }
        
        .image-container {
          margin-bottom: 10px;
        }
        
        .image-info {
          font-size: 10px;
        }
        
        .image-name {
          font-size: 11px;
        }
        
        .image-details {
          font-size: 9px;
        }
      }
    `
  }

  /**
   * 获取纸张大小CSS
   */
  static getPaperSizeCSS(paperSize) {
    const sizes = {
      'A4': 'A4',
      'A3': 'A3',
      'Letter': 'letter',
      '4x6': '4in 6in'
    }
    return sizes[paperSize] || 'A4'
  }

  /**
   * 获取图片容器宽度
   */
  static getImageContainerWidth(imagesPerPage) {
    const widths = {
      1: '100%',
      2: '48%',
      4: '48%',
      6: '30%',
      9: '30%'
    }
    return widths[imagesPerPage] || '48%'
  }

  /**
   * 获取图片最大高度
   */
  static getImageMaxHeight(imagesPerPage) {
    const heights = {
      1: '700px',
      2: '450px',
      4: '320px',
      6: '220px',
      9: '160px'
    }
    return heights[imagesPerPage] || '450px'
  }

  /**
   * 获取质量CSS
   */
  static getQualityCSS(quality) {
    const qualities = {
      'draft': 'image-rendering: pixelated;',
      'normal': 'image-rendering: auto;',
      'high': 'image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;',
      'best': 'image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;'
    }
    return qualities[quality] || 'image-rendering: auto;'
  }

  /**
   * 格式化日期时间
   */
  static formatDateTime(dateString) {
    if (!dateString) return ''
    
    try {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return dateString
    }
  }

  /**
   * 生成打印HTML
   */
  static generatePrintHTML(images, settings) {
    const imagesPerPage = settings.imagesPerPage.value
    const styles = this.generatePrintStyles(settings)
    
    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>图片打印 - ${images.length}张图片</title>
        <style>${styles}</style>
      </head>
      <body>
    `

    // 按每页图片数分组
    for (let i = 0; i < images.length; i += imagesPerPage) {
      const pageImages = images.slice(i, i + imagesPerPage)
      
      html += '<div class="print-page">'
      
      pageImages.forEach(image => {
        html += `
          <div class="image-container">
            <img class="print-image" src="${image.imageUrl}" alt="${image.imageName}" />
            <div class="image-info">
              <div class="image-name">${this.escapeHtml(image.imageName)}</div>
              <div class="image-details">
                工厂: ${this.escapeHtml(image.factoryName)} | 订单: ${this.escapeHtml(image.orderNumber)}
              </div>
              <div class="image-details">
                上传时间: ${this.formatDateTime(image.uploadDate)}
              </div>
              <div class="image-details">
                文件大小: ${image.fileSizeFormatted || '未知'}
              </div>
            </div>
          </div>
        `
      })
      
      html += '</div>'
    }

    html += `
      </body>
      </html>
    `

    return html
  }

  /**
   * HTML转义
   */
  static escapeHtml(text) {
    if (!text) return ''
    
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 执行Web打印
   */
  static async executePrint(images, settings) {
    try {
      // 预加载所有图片
      const imageUrls = images.map(img => img.imageUrl)
      await this.preloadImages(imageUrls)

      // 创建打印窗口
      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')
      
      if (!printWindow) {
        throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
      }

      // 生成打印HTML
      const printHTML = this.generatePrintHTML(images, settings)
      
      printWindow.document.write(printHTML)
      printWindow.document.close()

      // 等待图片加载完成后打印
      return new Promise((resolve, reject) => {
        printWindow.onload = () => {
          setTimeout(() => {
            try {
              printWindow.print()
              
              // 监听打印完成
              printWindow.onafterprint = () => {
                printWindow.close()
                resolve()
              }
              
              // 备用关闭机制
              setTimeout(() => {
                if (!printWindow.closed) {
                  printWindow.close()
                }
                resolve()
              }, 5000)
              
            } catch (error) {
              printWindow.close()
              reject(error)
            }
          }, 1000)
        }

        printWindow.onerror = (error) => {
          printWindow.close()
          reject(new Error('打印窗口加载失败: ' + error))
        }
      })

    } catch (error) {
      throw new Error('打印失败: ' + error.message)
    }
  }
}
