import{E as e,x as t,y as s,e as a}from"./index-BNdZE9oc.js";const r={...{baseURL:"http://47.110.47.26:8084/api",timeout:1e4},api:{factory:{list:"/factory/page",listAdmin:"/factory/page/admin",detail:"/factory",create:"/factory",update:"/factory",delete:"/factory",active:"/factory/active"},image:{list:"/image/page",detail:"/image",factories:"/image/factories",orders:"/image/orders",countByFactory:"/image/count/factory",countByOrder:"/image/count/order"},imageAccess:{url:"/image-access/url",urlSet:"/image-access/url-set",preload:"/image-access/preload",clearCache:"/image-access/cache"}}};const c=new class{constructor(){this.baseURL=r.baseURL,this.timeout=r.timeout,this.cache=new Map,this.pendingRequests=new Map,this.maxCacheSize=100,this.cacheTimeout=3e5}request(t={}){return new Promise(((s,a)=>{const r={url:"",method:"GET",data:{},header:{"Content-Type":"application/json"},timeout:this.timeout,cache:!0},c=Object.assign({},r,t);c.url.startsWith("http")||(c.url=this.baseURL+c.url);const i=this.generateCacheKey(c);if("GET"===c.method&&c.cache){const e=this.getCache(i);if(e)return s(e)}if(this.pendingRequests.has(i))return this.pendingRequests.get(i);this.requestInterceptor(c);const h=new Promise(((t,s)=>{e({...c,success:e=>{this.responseInterceptor(e,t,s,i,c)},fail:e=>{this.handleError(e,s)}})}));this.pendingRequests.set(i,h),h.finally((()=>{this.pendingRequests.delete(i)})),h.then(s).catch(a)}))}requestInterceptor(e){t({title:"加载中...",mask:!0})}responseInterceptor(e,t,a,r,c){s();const{data:i,statusCode:h}=e;200===h?200===i.code?("GET"===c.method&&c.cache&&this.setCache(r,i),t(i)):(this.showError(i.message||"请求失败"),a(i)):(this.showError(`请求失败，状态码：${h}`),a(e))}handleError(e,t){s();let a="网络请求失败";e.errMsg&&(e.errMsg.includes("timeout")?a="请求超时":e.errMsg.includes("fail")&&(a="网络连接失败")),this.showError(a),t(e)}showError(e){a({title:e,icon:"none",duration:2e3})}get(e,t={}){return this.request({url:e,method:"GET",data:t})}post(e,t={}){return this.request({url:e,method:"POST",data:t})}put(e,t={}){return this.request({url:e,method:"PUT",data:t})}delete(e,t={}){return this.request({url:e,method:"DELETE",data:t,cache:!1})}generateCacheKey(e){const{url:t,method:s,data:a}=e;return`${s}:${t}:${JSON.stringify(a)}`}getCache(e){const t=this.cache.get(e);return t&&Date.now()-t.timestamp<this.cacheTimeout?t.data:(t&&this.cache.delete(e),null)}setCache(e,t){if(this.cache.size>=this.maxCacheSize){const e=this.cache.keys().next().value;this.cache.delete(e)}this.cache.set(e,{data:t,timestamp:Date.now()})}clearCache(e){if(e)for(const t of this.cache.keys())t.includes(e)&&this.cache.delete(t);else this.cache.clear()}getCacheStats(){return{size:this.cache.size,maxSize:this.maxCacheSize,keys:Array.from(this.cache.keys())}}};export{r as c,c as r};
