package com.finance.controller;

import com.finance.common.Result;
import com.finance.service.ImageAccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 图片访问优化控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/image-access")
public class ImageAccessController {

    @Autowired
    private ImageAccessService imageAccessService;

    /**
     * 获取优化的图片URL
     * 
     * @param imagePath 图片路径
     * @param size 图片尺寸
     * @param request HTTP请求
     * @return 优化后的图片URL
     */
    @GetMapping("/url")
    public Result<String> getOptimizedImageUrl(@RequestParam String imagePath,
                                             @RequestParam(defaultValue = "medium") String size,
                                             HttpServletRequest request) {
        try {
            String optimizedUrl = imageAccessService.getOptimizedImageUrl(imagePath, size, request);
            return Result.success(optimizedUrl);
        } catch (Exception e) {
            log.error("获取优化图片URL失败: {}", e.getMessage(), e);
            return Result.error("获取图片URL失败");
        }
    }

    /**
     * 获取图片的多种尺寸URL
     * 
     * @param imagePath 图片路径
     * @param request HTTP请求
     * @return 包含不同尺寸URL的Map
     */
    @GetMapping("/url-set")
    public Result<Map<String, String>> getImageUrlSet(@RequestParam String imagePath,
                                                     HttpServletRequest request) {
        try {
            Map<String, String> urlSet = imageAccessService.getImageUrlSet(imagePath, request);
            return Result.success(urlSet);
        } catch (Exception e) {
            log.error("获取图片URL集合失败: {}", e.getMessage(), e);
            return Result.error("获取图片URL集合失败");
        }
    }

    /**
     * 批量预加载图片URL
     * 
     * @param imagePaths 图片路径数组
     * @param size 图片尺寸
     * @param request HTTP请求
     * @return 预加载的URL列表
     */
    @PostMapping("/preload")
    public Result<Map<String, String>> preloadImageUrls(@RequestBody String[] imagePaths,
                                                       @RequestParam(defaultValue = "medium") String size,
                                                       HttpServletRequest request) {
        try {
            Map<String, String> preloadUrls = imageAccessService.preloadImageUrls(imagePaths, size, request);
            return Result.success(preloadUrls);
        } catch (Exception e) {
            log.error("预加载图片URL失败: {}", e.getMessage(), e);
            return Result.error("预加载图片URL失败");
        }
    }

    /**
     * 清除图片URL缓存
     * 
     * @param imagePath 图片路径（可选）
     * @return 操作结果
     */
    @DeleteMapping("/cache")
    public Result<String> clearImageUrlCache(@RequestParam(required = false) String imagePath) {
        try {
            if (imagePath != null && !imagePath.isEmpty()) {
                imageAccessService.clearImageUrlCache(imagePath);
                return Result.success("清除指定图片缓存成功");
            } else {
                // 这里可以添加清除所有缓存的逻辑
                return Result.success("清除缓存成功");
            }
        } catch (Exception e) {
            log.error("清除图片缓存失败: {}", e.getMessage(), e);
            return Result.error("清除缓存失败");
        }
    }
}
