/**
 * 图片检索API
 */
import request from '@/utils/request.js'
import config from '@/config/api.js'

export default {
  /**
   * 分页查询图片信息
   */
  getImagePage(params) {
    return request.get(config.api.image.list, params)
  },

  /**
   * 根据ID获取图片信息
   */
  getImageById(id) {
    return request.get(`${config.api.image.detail}/${id}`)
  },

  /**
   * 获取所有工厂名称列表
   */
  getFactoryNames() {
    return request.get(config.api.image.factories)
  },

  /**
   * 根据工厂名称获取订单号列表
   */
  getOrderNumbersByFactory(factoryName) {
    return request.get(config.api.image.orders, { factoryName })
  },

  /**
   * 根据工厂名称统计图片数量
   */
  countByFactory(factoryName) {
    return request.get(config.api.image.countByFactory, { factoryName })
  },

  /**
   * 根据订单号统计图片数量
   */
  countByOrder(orderNumber) {
    return request.get(config.api.image.countByOrder, { orderNumber })
  }
}
