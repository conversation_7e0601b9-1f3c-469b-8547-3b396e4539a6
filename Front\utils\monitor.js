/**
 * 前端性能监控和错误处理工具
 */

class Monitor {
  constructor() {
    this.errors = []
    this.performance = {}
    this.init()
  }

  /**
   * 初始化监控
   */
  init() {
    this.setupErrorHandler()
    this.setupPerformanceMonitor()
  }

  /**
   * 设置错误处理
   */
  setupErrorHandler() {
    // 全局错误处理
    uni.onError((error) => {
      this.logError('Global Error', error)
    })

    // 未处理的Promise拒绝
    uni.onUnhandledRejection((event) => {
      this.logError('Unhandled Promise Rejection', event.reason)
    })
  }

  /**
   * 设置性能监控
   */
  setupPerformanceMonitor() {
    // 页面加载性能监控
    this.performance.pageLoadStart = Date.now()
  }

  /**
   * 记录错误
   */
  logError(type, error) {
    const errorInfo = {
      type,
      message: error.message || error,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    this.errors.push(errorInfo)

    // 可以发送到服务器
    this.reportError(errorInfo)
  }

  /**
   * 上报错误到服务器
   */
  async reportError(errorInfo) {
    try {
      // 这里可以调用API上报错误
      // 生产环境可以发送到错误监控服务
    } catch (e) {
      // 上报失败时静默处理
    }
  }

  /**
   * 记录性能指标
   */
  recordPerformance(name, value) {
    this.performance[name] = value
  }

  /**
   * 页面加载完成
   */
  pageLoadComplete() {
    const loadTime = Date.now() - this.performance.pageLoadStart
    this.recordPerformance('pageLoadTime', loadTime)
  }

  /**
   * API请求性能监控
   */
  wrapApiRequest(apiFunction) {
    return async (...args) => {
      const startTime = Date.now()
      try {
        const result = await apiFunction(...args)
        const duration = Date.now() - startTime
        this.recordPerformance('apiRequestTime', duration)
        return result
      } catch (error) {
        const duration = Date.now() - startTime
        this.recordPerformance('apiRequestTime', duration)
        this.logError('API Request Error', error)
        throw error
      }
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {}
    this.errors.forEach(error => {
      stats[error.type] = (stats[error.type] || 0) + 1
    })
    return stats
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return { ...this.performance }
  }

  /**
   * 清除历史数据
   */
  clear() {
    this.errors = []
    this.performance = {}
  }
}

// 创建全局监控实例
const monitor = new Monitor()

export default monitor
