<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-bar">
				<input 
					class="search-input" 
					placeholder="搜索工厂名称或用户名"
					v-model="searchQuery.keyword"
					@input="onSearchInput"
				/>
				<view class="search-btn" @click="handleSearch">
					<text class="search-icon">🔍</text>
				</view>
			</view>
			
			<view class="filter-bar">
				<picker 
					:value="statusIndex" 
					:range="statusOptions" 
					range-key="label"
					@change="onStatusChange"
				>
					<view class="filter-item">
						<text class="filter-label">状态：{{ statusOptions[statusIndex].label }}</text>
						<text class="filter-arrow">▼</text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 操作栏 -->
		<view class="action-bar">
			<view class="total-info">
				<text class="total-text">共 {{ pageData.total }} 条记录</text>
			</view>
			<view class="add-btn" @click="navigateToAdd">
				<text class="add-icon">+</text>
				<text class="add-text">新增工厂</text>
			</view>
		</view>

		<!-- 工厂列表 -->
		<view class="factory-list">
			<view 
				class="factory-item" 
				v-for="factory in factoryList" 
				:key="factory.id"
			>
				<view class="factory-header">
					<view class="factory-name">{{ factory.factoryName }}</view>
					<view class="factory-status" :class="factory.status === 1 ? 'status-active' : 'status-inactive'">
						{{ factory.status === 1 ? '启用' : '禁用' }}
					</view>
				</view>
				
				<view class="factory-info">
					<view class="info-row">
						<text class="info-label">用户名：</text>
						<text class="info-value">{{ factory.username }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">密码：</text>
						<text class="info-value">{{ factory.password || '未设置' }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">创建时间：</text>
						<text class="info-value">{{ formatDate(factory.createdAt) }}</text>
					</view>
				</view>
				
				<view class="factory-actions">
					<view class="action-btn edit-btn" @click="navigateToEdit(factory)">
						<text class="action-icon">✏️</text>
						<text class="action-text">编辑</text>
					</view>
					<view class="action-btn delete-btn" @click="confirmDelete(factory)">
						<text class="action-icon">🗑️</text>
						<text class="action-text">删除</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore" @click="loadMore">
			<text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
		</view>
		
		<!-- 无数据提示 -->
		<view class="empty-state" v-if="!loading && factoryList.length === 0">
			<text class="empty-icon">📭</text>
			<text class="empty-text">暂无工厂数据</text>
			<view class="empty-btn" @click="navigateToAdd">
				<text class="empty-btn-text">立即添加</text>
			</view>
		</view>
	</view>
</template>

<script>
import factoryApi from '@/api/factory.js'
import performanceOptimizer from '@/utils/performance.js'
import request from '@/utils/request.js'

export default {
	data() {
		return {
			factoryList: [],
			pageData: {
				current: 1,
				size: 20,
				total: 0,
				pages: 0
			},
			searchQuery: {
				keyword: '',
				status: null
			},
			statusOptions: [
				{ label: '全部', value: null },
				{ label: '启用', value: 1 },
				{ label: '禁用', value: 0 }
			],
			statusIndex: 0,
			loading: false,
			hasMore: false,
			searchTimer: null
		}
	},
	
	onLoad() {
		this.loadFactoryList()
	},
	
	onShow() {
		// 从其他页面返回时刷新列表
		// 清除缓存确保获取最新数据
		this.clearFactoryCache()
		this.refreshList()
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.refreshList()
	},
	
	methods: {
		/**
		 * 加载工厂列表
		 */
		async loadFactoryList(isLoadMore = false) {
			if (this.loading) return
			
			this.loading = true
			
			try {
				const params = {
					current: isLoadMore ? this.pageData.current + 1 : 1,
					size: this.pageData.size,
					...this.buildSearchParams()
				}
				
				const response = await factoryApi.getFactoryPageAdmin(params)
				const { data } = response
				
				if (isLoadMore) {
					this.factoryList = [...this.factoryList, ...data.records]
					this.pageData.current = data.current
				} else {
					this.factoryList = data.records
					this.pageData = data
				}
				
				this.hasMore = this.pageData.current < this.pageData.pages
				
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				uni.stopPullDownRefresh()
			}
		},
		
		/**
		 * 构建搜索参数
		 */
		buildSearchParams() {
			const params = {}
			
			if (this.searchQuery.keyword) {
				// 同时搜索工厂名称和用户名
				params.factoryName = this.searchQuery.keyword
				params.username = this.searchQuery.keyword
			}
			
			if (this.searchQuery.status !== null) {
				params.status = this.searchQuery.status
			}
			
			return params
		},
		
		/**
		 * 刷新列表
		 */
		refreshList() {
			// 清除缓存确保获取最新数据
			this.clearFactoryCache()
			this.pageData.current = 1
			this.loadFactoryList()
		},
		
		/**
		 * 加载更多
		 */
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.loadFactoryList(true)
			}
		},
		
		/**
		 * 搜索输入处理
		 */
		onSearchInput: performanceOptimizer.debounce(function() {
			this.handleSearch()
		}, 300),
		
		/**
		 * 执行搜索
		 */
		handleSearch() {
			this.refreshList()
		},
		
		/**
		 * 状态筛选变化
		 */
		onStatusChange(e) {
			this.statusIndex = e.detail.value
			this.searchQuery.status = this.statusOptions[this.statusIndex].value
			this.handleSearch()
		},
		
		/**
		 * 导航到新增页面
		 */
		navigateToAdd() {
			uni.navigateTo({
				url: '/pages/factory/form'
			})
		},
		
		/**
		 * 导航到编辑页面
		 */
		navigateToEdit(factory) {
			uni.navigateTo({
				url: `/pages/factory/form?id=${factory.id}`
			})
		},
		
		/**
		 * 确认删除
		 */
		confirmDelete(factory) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除工厂"${factory.factoryName}"吗？`,
				success: (res) => {
					if (res.confirm) {
						this.deleteFactory(factory.id)
					}
				}
			})
		},
		
		/**
		 * 删除工厂
		 */
		async deleteFactory(id) {
			try {
				await factoryApi.deleteFactory(id)

				// 清除相关缓存
				this.clearFactoryCache()

				// 从当前列表中移除已删除的项目，实现即时更新
				this.factoryList = this.factoryList.filter(factory => factory.id !== id)

				// 更新总数
				this.pageData.total = Math.max(0, this.pageData.total - 1)

				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})

				// 如果当前页没有数据了，且不是第一页，则加载上一页
				if (this.factoryList.length === 0 && this.pageData.current > 1) {
					this.pageData.current = this.pageData.current - 1
					this.loadFactoryList()
				}

			} catch (error) {
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				})
			}
		},
		
		/**
		 * 清除工厂相关缓存
		 */
		clearFactoryCache() {
			// 清除工厂列表相关的缓存
			request.clearCache('/factory')

			// 清除图片搜索页面可能缓存的工厂列表
			request.clearCache('/image/factories')
		},

		/**
		 * 格式化日期
		 */
		formatDate(dateString) {
			if (!dateString) return ''

			const date = new Date(dateString)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')

			return `${year}-${month}-${day}`
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;

	// PC端适配
	@media (min-width: 768px) {
		padding: 15px;
		max-width: 1200px;
		margin: 0 auto;
	}
}

.search-section {
	background: #ffffff;
	padding: 15px;
	margin-bottom: 15px;
	border-radius: 12px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

	@media (min-width: 768px) {
		padding: 20px;
		margin-bottom: 20px;
		border-radius: 16px;
	}
}

.search-bar {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 25px;
	padding: 0 15px;
	margin-bottom: 15px;
	transition: all 0.3s ease;

	&:focus-within {
		background: #e9ecef;
		box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
	}

	@media (min-width: 768px) {
		padding: 0 20px;
		margin-bottom: 18px;
	}
}

.search-input {
	flex: 1;
	height: 40px;
	font-size: 14px;
	color: #333;
	border: none;
	background: transparent;
	outline: none;

	&::placeholder {
		color: #999;
	}

	@media (min-width: 768px) {
		height: 45px;
		font-size: 16px;
	}
}

.search-btn {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: transform 0.2s ease;

	&:hover {
		transform: scale(1.05);
	}

	@media (min-width: 768px) {
		width: 36px;
		height: 36px;
	}
}

.search-icon {
	font-size: 16px;
	color: #007AFF;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}

.filter-bar {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;

	@media (min-width: 768px) {
		gap: 15px;
	}
}

.filter-item {
	display: flex;
	align-items: center;
	padding: 8px 15px;
	background: #f8f9fa;
	border-radius: 20px;
	font-size: 12px;
	color: #666;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		background: #e9ecef;
		color: #333;
	}

	@media (min-width: 768px) {
		padding: 10px 18px;
		font-size: 14px;
	}
}

.filter-arrow {
	margin-left: 6px;
	font-size: 10px;

	@media (min-width: 768px) {
		margin-left: 8px;
		font-size: 12px;
	}
}

.action-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 15px 15px;

	@media (min-width: 768px) {
		padding: 0 0 20px;
	}
}

.total-text {
	font-size: 12px;
	color: #666;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.add-btn {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, #007AFF, #0056CC);
	color: #ffffff;
	padding: 8px 15px;
	border-radius: 20px;
	font-size: 12px;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2px 10px rgba(0, 122, 255, 0.3);

	&:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 15px rgba(0, 122, 255, 0.4);
	}

	@media (min-width: 768px) {
		padding: 10px 18px;
		font-size: 14px;
		border-radius: 22px;
	}
}

.add-icon {
	margin-right: 6px;
	font-size: 14px;

	@media (min-width: 768px) {
		margin-right: 8px;
		font-size: 16px;
	}
}

.factory-list {
	padding: 0 15px;
	display: grid;
	gap: 15px;

	@media (min-width: 768px) {
		padding: 0;
		grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
		gap: 18px;
	}

	@media (min-width: 1200px) {
		grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
		gap: 20px;
	}
}

.factory-item {
	background: #ffffff;
	border-radius: 12px;
	padding: 18px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
	cursor: pointer;

	&:hover {
		transform: translateY(-3px);
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	}

	@media (min-width: 768px) {
		padding: 22px;
		border-radius: 16px;
	}
}

.factory-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;

	@media (min-width: 768px) {
		margin-bottom: 18px;
	}
}

.factory-name {
	font-size: 16px;
	font-weight: 700;
	color: #333;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}

.factory-status {
	padding: 4px 10px;
	border-radius: 12px;
	font-size: 10px;
	font-weight: 600;

	@media (min-width: 768px) {
		padding: 6px 12px;
		border-radius: 15px;
		font-size: 12px;
	}
}

.status-active {
	background: linear-gradient(135deg, #e6f7ff, #bae7ff);
	color: #1890ff;
}

.status-inactive {
	background: linear-gradient(135deg, #fff2e8, #ffd8bf);
	color: #fa8c16;
}

.factory-info {
	margin-bottom: 18px;

	@media (min-width: 768px) {
		margin-bottom: 22px;
	}
}

.info-row {
	display: flex;
	margin-bottom: 6px;
	font-size: 12px;

	@media (min-width: 768px) {
		margin-bottom: 8px;
		font-size: 14px;
	}
}

.info-label {
	color: #666;
	width: 100px;
	font-weight: 500;

	@media (min-width: 768px) {
		width: 120px;
	}
}

.info-value {
	color: #333;
	flex: 1;
	font-weight: 400;
}

.factory-actions {
	display: flex;
	gap: 15px;

	@media (min-width: 768px) {
		gap: 20px;
	}
}

.action-btn {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 12px;
	border-radius: 12px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}

	@media (min-width: 768px) {
		padding: 15px;
		border-radius: 15px;
		font-size: 16px;
	}
}

.edit-btn {
	background: linear-gradient(135deg, #e6f7ff, #bae7ff);
	color: #1890ff;

	&:hover {
		box-shadow: 0 4px 15px rgba(24, 144, 255, 0.3);
	}
}

.delete-btn {
	background: linear-gradient(135deg, #fff2f0, #ffccc7);
	color: #ff4d4f;

	&:hover {
		box-shadow: 0 4px 15px rgba(255, 77, 79, 0.3);
	}
}

.action-icon {
	margin-right: 6px;

	@media (min-width: 768px) {
		margin-right: 8px;
	}
}

.load-more {
	text-align: center;
	padding: 40px;
	color: #666;
	font-size: 16px;
	cursor: pointer;
	transition: color 0.3s ease;

	&:hover {
		color: #007AFF;
	}

	@media (min-width: 768px) {
		padding: 50px;
		font-size: 18px;
	}
}

.empty-state {
	text-align: center;
	padding: 80px 20px;

	@media (min-width: 768px) {
		padding: 120px 30px;
	}
}

.empty-icon {
	font-size: 80px;
	display: block;
	margin-bottom: 20px;

	@media (min-width: 768px) {
		font-size: 120px;
		margin-bottom: 30px;
	}
}

.empty-text {
	font-size: 20px;
	color: #666;
	display: block;
	margin-bottom: 30px;

	@media (min-width: 768px) {
		font-size: 24px;
		margin-bottom: 40px;
	}
}

.empty-btn {
	display: inline-block;
	background: linear-gradient(135deg, #007AFF, #0056CC);
	color: #ffffff;
	padding: 15px 30px;
	border-radius: 25px;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
	}

	@media (min-width: 768px) {
		padding: 18px 35px;
		font-size: 18px;
		border-radius: 30px;
	}
}
</style>
