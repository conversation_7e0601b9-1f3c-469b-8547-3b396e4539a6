package com.finance.util;

import java.text.DecimalFormat;

/**
 * 文件工具类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class FileUtil {

    /**
     * 格式化文件大小
     * 
     * @param fileSize 文件大小（字节）
     * @return 格式化后的文件大小
     */
    public static String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }

        final String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(fileSize) / Math.log10(1024));
        
        if (digitGroups >= units.length) {
            digitGroups = units.length - 1;
        }
        
        double size = fileSize / Math.pow(1024, digitGroups);
        DecimalFormat df = new DecimalFormat("#,##0.#");
        
        return df.format(size) + " " + units[digitGroups];
    }

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名（不包含点号）
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 检查是否为图片文件
     * 
     * @param fileName 文件名
     * @return 是否为图片文件
     */
    public static boolean isImageFile(String fileName) {
        String extension = getFileExtension(fileName);
        return extension.matches("jpg|jpeg|png|gif|bmp|webp");
    }

    /**
     * 生成图片访问URL
     * 
     * @param imagePath 图片路径
     * @param baseUrl   基础URL
     * @return 图片访问URL
     */
    public static String generateImageUrl(String imagePath, String baseUrl) {
        if (imagePath == null || imagePath.isEmpty()) {
            return null;
        }
        
        // 确保路径使用正斜杠
        String normalizedPath = imagePath.replace("\\", "/");
        
        // 确保baseUrl以斜杠结尾
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        
        // 确保路径不以斜杠开头
        if (normalizedPath.startsWith("/")) {
            normalizedPath = normalizedPath.substring(1);
        }
        
        return baseUrl + normalizedPath;
    }
}
