<template>
	<view class="container">
		<!-- 头部欢迎区域 -->
		<view class="header-section">
			<view class="welcome-card">
				<view class="welcome-content">
					<text class="welcome-title">订单图片管理系统</text>
					<text class="welcome-subtitle">Order Image Management System</text>
					<text class="welcome-desc">高效管理订单图片，轻松检索文件信息</text>
				</view>
				<view class="welcome-icon">
					<text class="icon-text">📋</text>
				</view>
			</view>
		</view>

		<!-- 功能模块区域 -->
		<view class="modules-section">
			<view class="section-title">
				<text class="title-text">功能模块</text>
				<text class="title-desc">选择您需要的功能模块</text>
			</view>

			<view class="modules-grid">
				<view class="module-item" @click="navigateToFactory">
					<view class="module-header">
						<view class="module-icon factory-icon">
							<text class="icon-text">🏭</text>
						</view>
						<view class="module-badge">管理</view>
					</view>
					<view class="module-content">
						<text class="module-title">工厂管理</text>
						<text class="module-desc">管理工厂信息，维护基础数据</text>
						<view class="module-features">
							<text class="feature-item">• 新增工厂</text>
							<text class="feature-item">• 编辑信息</text>
							<text class="feature-item">• 状态管理</text>
						</view>
					</view>
					<view class="module-footer">
						<text class="action-text">立即管理 →</text>
					</view>
				</view>

				<view class="module-item" @click="navigateToImageSearch">
					<view class="module-header">
						<view class="module-icon search-icon">
							<text class="icon-text">🔍</text>
						</view>
						<view class="module-badge">检索</view>
					</view>
					<view class="module-content">
						<text class="module-title">图片检索</text>
						<text class="module-desc">快速查找订单图片，支持多条件搜索</text>
						<view class="module-features">
							<text class="feature-item">• 智能搜索</text>
							<text class="feature-item">• 图片预览</text>
							<text class="feature-item">• 批量操作</text>
						</view>
					</view>
					<view class="module-footer">
						<text class="action-text">开始检索 →</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 移除统计数据
		}
	},

	onLoad() {
		// 页面加载完成
	},

	methods: {
		/**
		 * 导航到工厂管理
		 */
		navigateToFactory() {
			uni.switchTab({
				url: '/pages/factory/list'
			})
		},

		/**
		 * 导航到图片检索
		 */
		navigateToImageSearch() {
			uni.switchTab({
				url: '/pages/image/search'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20px 15px;

	// PC端适配
	@media (min-width: 768px) {
		padding: 30px 20px;
		max-width: 1200px;
		margin: 0 auto;
	}

	@media (min-width: 1200px) {
		padding: 40px 30px;
	}
}

.header-section {
	margin-bottom: 30px;

	@media (min-width: 768px) {
		margin-bottom: 40px;
	}
}

.welcome-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16px;
	padding: 25px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
	transition: transform 0.3s ease, box-shadow 0.3s ease;

	&:hover {
		transform: translateY(-3px);
		box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
	}

	@media (min-width: 768px) {
		padding: 35px;
		border-radius: 20px;
	}
}

.welcome-content {
	flex: 1;
}

.welcome-title {
	font-size: 24px;
	font-weight: 700;
	color: #2c3e50;
	display: block;
	margin-bottom: 8px;

	@media (min-width: 768px) {
		font-size: 32px;
		margin-bottom: 10px;
	}
}

.welcome-subtitle {
	font-size: 14px;
	color: #7f8c8d;
	display: block;
	margin-bottom: 6px;

	@media (min-width: 768px) {
		font-size: 16px;
		margin-bottom: 8px;
	}
}

.welcome-desc {
	font-size: 12px;
	color: #95a5a6;
	display: block;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.welcome-icon {
	width: 80px;
	height: 80px;
	margin-left: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 50%;

	@media (min-width: 768px) {
		width: 100px;
		height: 100px;
		margin-left: 25px;
	}

	.icon-text {
		font-size: 40px;

		@media (min-width: 768px) {
			font-size: 50px;
		}
	}
}

.modules-section {
	margin-bottom: 30px;

	@media (min-width: 768px) {
		margin-bottom: 40px;
	}
}

.section-title {
	margin-bottom: 25px;
	text-align: center;

	@media (min-width: 768px) {
		margin-bottom: 30px;
	}
}

.title-text {
	font-size: 22px;
	font-weight: 700;
	color: #ffffff;
	display: block;
	margin-bottom: 6px;

	@media (min-width: 768px) {
		font-size: 28px;
		margin-bottom: 8px;
	}
}

.title-desc {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.8);
	display: block;

	@media (min-width: 768px) {
		font-size: 16px;
	}
}

.modules-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 20px;

	@media (min-width: 768px) {
		grid-template-columns: repeat(2, 1fr);
		gap: 25px;
	}

	@media (min-width: 1200px) {
		gap: 30px;
	}
}

.module-item {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16px;
	padding: 25px;
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	cursor: pointer;
	backdrop-filter: blur(10px);

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);

		.module-icon {
			transform: scale(1.05);
		}
	}

	@media (min-width: 768px) {
		padding: 30px;
		border-radius: 20px;
	}
}

.module-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20px;
}

.module-icon {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: transform 0.3s ease;

	@media (min-width: 768px) {
		width: 70px;
		height: 70px;
	}

	.icon-text {
		font-size: 28px;

		@media (min-width: 768px) {
			font-size: 32px;
		}
	}
}

.factory-icon {
	background: linear-gradient(135deg, #667eea, #764ba2);
}

.search-icon {
	background: linear-gradient(135deg, #f093fb, #f5576c);
}

.module-badge {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 6px 12px;
	border-radius: 16px;
	font-size: 12px;
	font-weight: 600;
}

.module-content {
	margin-bottom: 20px;
}

.module-title {
	font-size: 20px;
	font-weight: 700;
	color: #2c3e50;
	display: block;
	margin-bottom: 8px;

	@media (min-width: 768px) {
		font-size: 24px;
		margin-bottom: 10px;
	}
}

.module-desc {
	font-size: 14px;
	color: #7f8c8d;
	display: block;
	margin-bottom: 15px;
	line-height: 1.4;

	@media (min-width: 768px) {
		font-size: 16px;
		margin-bottom: 18px;
	}
}

.module-features {
	display: flex;
	flex-direction: column;
	gap: 6px;
}

.feature-item {
	font-size: 12px;
	color: #95a5a6;

	@media (min-width: 768px) {
		font-size: 14px;
	}
}

.module-footer {
	text-align: right;
}

.action-text {
	font-size: 16px;
	color: #667eea;
	font-weight: 600;

	@media (min-width: 768px) {
		font-size: 18px;
	}
}


</style>
