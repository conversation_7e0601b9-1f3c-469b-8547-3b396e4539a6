import{e as a,m as t,c as s,w as e,i as l,o,a as i,b as r,t as d,j as n,d as c,I as u,k as f}from"./index-DjruG92H.js";import{f as m}from"./factory.o0XEgZaa.js";import{r as h}from"./request.j3WTH_xy.js";import{_ as p}from"./_plugin-vue_export-helper.BCo6x5W8.js";const _=p({data:()=>({isEdit:!1,factoryId:null,formData:{username:"",password:"",factoryName:"",status:1},statusOptions:[{label:"启用",value:1},{label:"禁用",value:0}],statusIndex:0,submitting:!1,showPassword:!1}),onLoad(a){a.id&&(this.isEdit=!0,this.factoryId=parseInt(a.id),this.loadFactoryDetail())},methods:{async loadFactoryDetail(){try{const a=(await m.getFactoryById(this.factoryId)).data;this.formData={username:a.username,password:"",factoryName:a.factoryName,status:a.status},this.statusIndex=this.statusOptions.findIndex((t=>t.value===a.status))}catch(t){a({title:"加载失败",icon:"none"})}},onStatusChange(a){this.statusIndex=a.detail.value,this.formData.status=this.statusOptions[this.statusIndex].value},validateForm(){return this.formData.username.trim()?this.isEdit||this.formData.password.trim()?this.formData.password&&this.formData.password.length<6?(a({title:"密码长度至少6位",icon:"none"}),!1):!!this.formData.factoryName.trim()||(a({title:"请输入工厂名称",icon:"none"}),!1):(a({title:"请输入密码",icon:"none"}),!1):(a({title:"请输入用户名",icon:"none"}),!1)},async handleSubmit(){if(this.validateForm()&&!this.submitting){this.submitting=!0;try{const s={...this.formData};this.isEdit&&!s.password&&delete s.password,this.isEdit?(await m.updateFactory(this.factoryId,s),a({title:"更新成功",icon:"success"})):(await m.createFactory(s),a({title:"创建成功",icon:"success"})),this.clearFactoryCache(),setTimeout((()=>{t()}),1500)}catch(s){a({title:this.isEdit?"更新失败":"创建失败",icon:"none"})}finally{this.submitting=!1}}},togglePasswordVisibility(){this.showPassword=!this.showPassword},clearFactoryCache(){h.clearCache("/factory"),h.clearCache("/image/factories")},handleCancel(){t()}}},[["render",function(a,t,m,h,p,_){const y=c,w=l,b=u,g=f;return o(),s(w,{class:"container"},{default:e((()=>[i(w,{class:"form-card"},{default:e((()=>[i(w,{class:"form-header"},{default:e((()=>[i(y,{class:"form-title"},{default:e((()=>[r(d(p.isEdit?"编辑工厂":"新增工厂"),1)])),_:1})])),_:1}),i(w,{class:"form-content"},{default:e((()=>[i(w,{class:"form-item"},{default:e((()=>[i(y,{class:"form-label"},{default:e((()=>[r("用户名 "),i(y,{class:"required"},{default:e((()=>[r("*")])),_:1})])),_:1}),i(b,{class:"form-input",placeholder:"请输入用户名",modelValue:p.formData.username,"onUpdate:modelValue":t[0]||(t[0]=a=>p.formData.username=a)},null,8,["modelValue"])])),_:1}),i(w,{class:"form-item"},{default:e((()=>[i(y,{class:"form-label"},{default:e((()=>[r(" 密码 "),p.isEdit?n("",!0):(o(),s(y,{key:0,class:"required"},{default:e((()=>[r("*")])),_:1})),p.isEdit?(o(),s(y,{key:1,class:"optional"},{default:e((()=>[r("（留空则不修改）")])),_:1})):n("",!0)])),_:1}),i(w,{class:"password-input-wrapper"},{default:e((()=>[i(b,{class:"form-input password-input",type:p.showPassword?"text":"password",placeholder:"请输入密码",modelValue:p.formData.password,"onUpdate:modelValue":t[1]||(t[1]=a=>p.formData.password=a)},null,8,["type","modelValue"]),i(w,{class:"password-toggle",onClick:_.togglePasswordVisibility},{default:e((()=>[i(y,{class:"eye-icon"},{default:e((()=>[r(d(p.showPassword?"🙈":"👁"),1)])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1}),i(w,{class:"form-item"},{default:e((()=>[i(y,{class:"form-label"},{default:e((()=>[r("工厂名称 "),i(y,{class:"required"},{default:e((()=>[r("*")])),_:1})])),_:1}),i(b,{class:"form-input",placeholder:"请输入工厂名称",modelValue:p.formData.factoryName,"onUpdate:modelValue":t[2]||(t[2]=a=>p.formData.factoryName=a)},null,8,["modelValue"])])),_:1}),i(w,{class:"form-item"},{default:e((()=>[i(y,{class:"form-label"},{default:e((()=>[r("状态 "),i(y,{class:"required"},{default:e((()=>[r("*")])),_:1})])),_:1}),i(g,{value:p.statusIndex,range:p.statusOptions,"range-key":"label",onChange:_.onStatusChange},{default:e((()=>[i(w,{class:"picker-input"},{default:e((()=>[i(y,{class:"picker-text"},{default:e((()=>[r(d(p.statusOptions[p.statusIndex].label),1)])),_:1}),i(y,{class:"picker-arrow"},{default:e((()=>[r("▼")])),_:1})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1})])),_:1}),i(w,{class:"form-actions"},{default:e((()=>[i(w,{class:"action-btn cancel-btn",onClick:_.handleCancel},{default:e((()=>[i(y,{class:"btn-text"},{default:e((()=>[r("取消")])),_:1})])),_:1},8,["onClick"]),i(w,{class:"action-btn submit-btn",onClick:_.handleSubmit},{default:e((()=>[i(y,{class:"btn-text"},{default:e((()=>[r(d(p.isEdit?"更新":"创建"),1)])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-0ced7b22"]]);export{_ as default};
