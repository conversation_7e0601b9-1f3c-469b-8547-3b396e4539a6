package com.finance.service;

import com.finance.common.PageResult;
import com.finance.dto.FactoryDTO;
import com.finance.dto.FactoryQueryDTO;
import com.finance.entity.Factory;
import com.finance.repository.FactoryRepository;
import com.finance.util.PasswordUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工厂服务类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@Transactional
public class FactoryService {

    @Autowired
    private FactoryRepository factoryRepository;

    /**
     * 分页查询工厂列表
     */
    public PageResult<FactoryDTO> getFactoryPage(FactoryQueryDTO queryDTO) {
        // 创建分页对象
        Pageable pageable = PageRequest.of(
            queryDTO.getCurrent() - 1, // Spring Data页码从0开始
            queryDTO.getSize(),
            Sort.by(Sort.Direction.DESC, "createdAt")
        );

        // 查询数据
        Page<Factory> factoryPage = factoryRepository.findByConditions(
            queryDTO.getUsername(),
            queryDTO.getFactoryName(),
            queryDTO.getStatus(),
            pageable
        );

        // 转换为DTO
        List<FactoryDTO> factoryDTOs = factoryPage.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());

        return new PageResult<>(
            factoryDTOs,
            factoryPage.getTotalElements(),
            queryDTO.getCurrent(),
            queryDTO.getSize()
        );
    }

    /**
     * 根据ID获取工厂信息
     */
    public FactoryDTO getFactoryById(Integer id) {
        Factory factory = factoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("工厂不存在"));
        return convertToDTO(factory);
    }

    /**
     * 新增工厂
     */
    public FactoryDTO createFactory(FactoryDTO factoryDTO) {
        // 检查用户名是否已存在
        if (factoryRepository.existsByUsername(factoryDTO.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 验证密码
        if (!StringUtils.hasText(factoryDTO.getPassword())) {
            throw new RuntimeException("密码不能为空");
        }

        Factory factory = new Factory();
        BeanUtils.copyProperties(factoryDTO, factory);
        
        // 直接存储密码，不进行加密
        factory.setPassword(factoryDTO.getPassword());
        
        Factory savedFactory = factoryRepository.save(factory);
        log.info("新增工厂成功，ID: {}, 用户名: {}", savedFactory.getId(), savedFactory.getUsername());
        
        return convertToDTO(savedFactory);
    }

    /**
     * 更新工厂信息
     */
    public FactoryDTO updateFactory(Integer id, FactoryDTO factoryDTO) {
        Factory existingFactory = factoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("工厂不存在"));

        // 检查用户名是否被其他工厂使用
        if (!existingFactory.getUsername().equals(factoryDTO.getUsername()) &&
            factoryRepository.existsByUsername(factoryDTO.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 更新基本信息
        existingFactory.setUsername(factoryDTO.getUsername());
        existingFactory.setFactoryName(factoryDTO.getFactoryName());
        existingFactory.setStatus(factoryDTO.getStatus());

        // 如果提供了新密码，则更新密码
        if (StringUtils.hasText(factoryDTO.getPassword())) {
            existingFactory.setPassword(factoryDTO.getPassword());
        }

        Factory savedFactory = factoryRepository.save(existingFactory);
        log.info("更新工厂成功，ID: {}, 用户名: {}", savedFactory.getId(), savedFactory.getUsername());
        
        return convertToDTO(savedFactory);
    }

    /**
     * 删除工厂
     */
    public void deleteFactory(Integer id) {
        if (!factoryRepository.existsById(id)) {
            throw new RuntimeException("工厂不存在");
        }
        
        factoryRepository.deleteById(id);
        log.info("删除工厂成功，ID: {}", id);
    }

    /**
     * 获取所有启用的工厂列表
     */
    public List<FactoryDTO> getActiveFactories() {
        List<Factory> factories = factoryRepository.findByStatus(1,
            PageRequest.of(0, Integer.MAX_VALUE, Sort.by(Sort.Direction.ASC, "factoryName")))
            .getContent();

        return factories.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    /**
     * 分页查询工厂列表（包含密码信息，仅用于管理界面）
     */
    public PageResult<FactoryDTO> getFactoryPageWithPassword(FactoryQueryDTO queryDTO) {
        // 创建分页对象
        Pageable pageable = PageRequest.of(
            queryDTO.getCurrent() - 1, // Spring Data页码从0开始
            queryDTO.getSize(),
            Sort.by(Sort.Direction.DESC, "createdAt")
        );

        // 查询数据
        Page<Factory> factoryPage = factoryRepository.findByConditions(
            queryDTO.getUsername(),
            queryDTO.getFactoryName(),
            queryDTO.getStatus(),
            pageable
        );

        // 转换为DTO（包含密码）
        List<FactoryDTO> factoryDTOs = factoryPage.getContent().stream()
            .map(this::convertToDTOWithPassword)
            .collect(Collectors.toList());

        return new PageResult<>(
            factoryDTOs,
            factoryPage.getTotalElements(),
            queryDTO.getCurrent(),
            queryDTO.getSize()
        );
    }

    /**
     * 实体转DTO
     */
    private FactoryDTO convertToDTO(Factory factory) {
        FactoryDTO dto = new FactoryDTO();
        BeanUtils.copyProperties(factory, dto);
        // 不返回密码
        dto.setPassword(null);
        return dto;
    }

    /**
     * 实体转DTO（包含密码信息，用于管理界面）
     */
    private FactoryDTO convertToDTOWithPassword(Factory factory) {
        FactoryDTO dto = new FactoryDTO();
        BeanUtils.copyProperties(factory, dto);
        // 返回真实密码
        dto.setPassword(factory.getPassword());
        return dto;
    }
}
