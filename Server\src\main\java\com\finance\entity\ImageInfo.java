package com.finance.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 图片信息实体类
 * 对应数据库表：Img_Info
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "Img_Info")
public class ImageInfo {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 工厂名称
     */
    @NotBlank(message = "工厂名称不能为空")
    @Size(max = 100, message = "工厂名称长度不能超过100个字符")
    @Column(name = "factory_name", nullable = false, length = 100)
    private String factoryName;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Size(max = 100, message = "订单号长度不能超过100个字符")
    @Column(name = "order_number", nullable = false, length = 100)
    private String orderNumber;

    /**
     * 图片文件名
     */
    @NotBlank(message = "图片文件名不能为空")
    @Size(max = 255, message = "图片文件名长度不能超过255个字符")
    @Column(name = "image_name", nullable = false)
    private String imageName;

    /**
     * 图片完整路径
     */
    @NotBlank(message = "图片路径不能为空")
    @Size(max = 500, message = "图片路径长度不能超过500个字符")
    @Column(name = "image_path", nullable = false, length = 500)
    private String imagePath;

    /**
     * 文件大小(字节)
     */
    @Column(name = "file_size")
    private Long fileSize = 0L;

    /**
     * 上传日期
     */
    @Column(name = "upload_date")
    private LocalDateTime uploadDate;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
}
