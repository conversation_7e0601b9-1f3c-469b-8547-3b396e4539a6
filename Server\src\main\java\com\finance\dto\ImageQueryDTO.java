package com.finance.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 图片查询条件DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class ImageQueryDTO {

    /**
     * 工厂名称（精确匹配）
     */
    private String factoryName;

    /**
     * 订单号（模糊查询）
     */
    private String orderNumber;

    /**
     * 图片文件名（模糊查询）
     */
    private String imageName;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 页码（从1开始）
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 20;
}
