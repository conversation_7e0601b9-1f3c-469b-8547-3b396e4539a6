/**
 * 数据验证工具
 */

export default {
  /**
   * 验证是否为空
   */
  isEmpty(value) {
    return value === null || value === undefined || value === '' || 
           (Array.isArray(value) && value.length === 0) ||
           (typeof value === 'object' && Object.keys(value).length === 0)
  },

  /**
   * 验证字符串长度
   */
  isLength(value, min = 0, max = Infinity) {
    if (typeof value !== 'string') return false
    return value.length >= min && value.length <= max
  },

  /**
   * 验证邮箱格式
   */
  isEmail(value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value)
  },

  /**
   * 验证手机号格式
   */
  isPhone(value) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(value)
  },

  /**
   * 验证密码强度
   */
  isStrongPassword(value) {
    // 至少8位，包含大小写字母、数字和特殊字符
    const strongRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    return strongRegex.test(value)
  },

  /**
   * 验证用户名格式
   */
  isUsername(value) {
    // 3-20位，字母、数字、下划线
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
    return usernameRegex.test(value)
  },

  /**
   * 验证数字范围
   */
  isNumberInRange(value, min = -Infinity, max = Infinity) {
    const num = Number(value)
    return !isNaN(num) && num >= min && num <= max
  },

  /**
   * 验证日期格式
   */
  isDate(value) {
    const date = new Date(value)
    return date instanceof Date && !isNaN(date)
  },

  /**
   * 验证URL格式
   */
  isURL(value) {
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  },



  /**
   * 工厂表单验证
   */
  validateFactory(data) {
    const errors = {}

    if (this.isEmpty(data.username)) {
      errors.username = '用户名不能为空'
    } else if (!this.isUsername(data.username)) {
      errors.username = '用户名格式不正确（3-20位字母、数字、下划线）'
    }

    if (this.isEmpty(data.password) && !data.id) {
      errors.password = '密码不能为空'
    } else if (data.password && !this.isLength(data.password, 6, 20)) {
      errors.password = '密码长度必须在6-20位之间'
    }

    if (this.isEmpty(data.factoryName)) {
      errors.factoryName = '工厂名称不能为空'
    } else if (!this.isLength(data.factoryName, 1, 100)) {
      errors.factoryName = '工厂名称长度不能超过100个字符'
    }

    if (data.status === null || data.status === undefined) {
      errors.status = '请选择状态'
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  },

  /**
   * 图片搜索表单验证
   */
  validateImageSearch(data) {
    const errors = {}

    if (data.orderNumber && !this.isLength(data.orderNumber, 1, 100)) {
      errors.orderNumber = '订单号长度不能超过100个字符'
    }

    if (data.imageName && !this.isLength(data.imageName, 1, 255)) {
      errors.imageName = '图片名称长度不能超过255个字符'
    }

    if (data.startDate && data.endDate) {
      const startDate = new Date(data.startDate)
      const endDate = new Date(data.endDate)
      
      if (startDate > endDate) {
        errors.dateRange = '开始日期不能大于结束日期'
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  },

  /**
   * 通用表单验证
   */
  validate(data, rules) {
    const errors = {}

    for (const field in rules) {
      const value = data[field]
      const fieldRules = rules[field]

      for (const rule of fieldRules) {
        if (rule.required && this.isEmpty(value)) {
          errors[field] = rule.message || `${field}不能为空`
          break
        }

        if (!this.isEmpty(value) && rule.validator && !rule.validator(value)) {
          errors[field] = rule.message || `${field}格式不正确`
          break
        }
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  }
}
