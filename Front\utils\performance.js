/**
 * 前端性能优化工具类
 */

class PerformanceOptimizer {
  constructor() {
    this.timers = new Map()
    this.observers = new Map()
    this.metrics = {
      renderTime: [],
      apiTime: [],
      memoryUsage: []
    }
  }

  /**
   * 防抖函数
   * @param {Function} func 要防抖的函数
   * @param {number} delay 延迟时间(ms)
   * @param {boolean} immediate 是否立即执行
   */
  debounce(func, delay = 300, immediate = false) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        timeout = null
        if (!immediate) func.apply(this, args)
      }
      const callNow = immediate && !timeout
      clearTimeout(timeout)
      timeout = setTimeout(later, delay)
      if (callNow) func.apply(this, args)
    }
  }

  /**
   * 节流函数
   * @param {Function} func 要节流的函数
   * @param {number} limit 时间间隔(ms)
   */
  throttle(func, limit = 100) {
    let inThrottle
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  /**
   * 延迟执行
   * @param {Function} func 要延迟执行的函数
   * @param {number} delay 延迟时间(ms)
   */
  defer(func, delay = 0) {
    return setTimeout(func, delay)
  }

  /**
   * 下一帧执行
   * @param {Function} func 要在下一帧执行的函数
   */
  nextFrame(func) {
    // #ifdef H5
    if (typeof requestAnimationFrame !== 'undefined') {
      return requestAnimationFrame(func)
    }
    // #endif
    return setTimeout(func, 16) // 约60fps
  }

  /**
   * 批量DOM更新
   * @param {Function} func 包含DOM操作的函数
   */
  batchUpdate(func) {
    this.nextFrame(() => {
      func()
    })
  }

  /**
   * 虚拟滚动计算
   * @param {Object} options 配置选项
   */
  calculateVirtualScroll(options) {
    const {
      totalItems,
      itemHeight,
      containerHeight,
      scrollTop,
      overscan = 5
    } = options

    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(totalItems, startIndex + visibleCount + overscan * 2)

    return {
      startIndex,
      endIndex,
      visibleCount,
      offsetY: startIndex * itemHeight
    }
  }

  /**
   * 图片懒加载观察器
   * @param {Element} target 目标元素
   * @param {Function} callback 回调函数
   * @param {Object} options 配置选项
   */
  observeImage(target, callback, options = {}) {
    const defaultOptions = {
      rootMargin: '50px',
      threshold: 0.1
    }

    const observerOptions = { ...defaultOptions, ...options }

    // #ifdef H5
    if (typeof IntersectionObserver !== 'undefined') {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            callback(entry.target)
            observer.unobserve(entry.target)
          }
        })
      }, observerOptions)

      observer.observe(target)
      return observer
    }
    // #endif

    // 降级处理
    callback(target)
    return null
  }

  /**
   * 内存使用监控
   */
  monitorMemory() {
    // #ifdef H5
    if (performance.memory) {
      const memory = {
        used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
      }
      
      this.metrics.memoryUsage.push({
        ...memory,
        timestamp: Date.now()
      })
      
      return memory
    }
    // #endif
    
    return null
  }

  /**
   * 性能计时开始
   * @param {string} name 计时器名称
   */
  timeStart(name) {
    this.timers.set(name, Date.now())
  }

  /**
   * 性能计时结束
   * @param {string} name 计时器名称
   */
  timeEnd(name) {
    const startTime = this.timers.get(name)
    if (startTime) {
      const duration = Date.now() - startTime
      this.timers.delete(name)
      
      // 记录到对应的指标中
      if (name.includes('render')) {
        this.metrics.renderTime.push(duration)
      } else if (name.includes('api')) {
        this.metrics.apiTime.push(duration)
      }
      
      return duration
    }
    return 0
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    const calculateStats = (arr) => {
      if (arr.length === 0) return { avg: 0, min: 0, max: 0, count: 0 }
      
      const sum = arr.reduce((a, b) => a + b, 0)
      return {
        avg: Math.round(sum / arr.length),
        min: Math.min(...arr),
        max: Math.max(...arr),
        count: arr.length
      }
    }

    return {
      renderTime: calculateStats(this.metrics.renderTime),
      apiTime: calculateStats(this.metrics.apiTime),
      memoryUsage: this.metrics.memoryUsage.slice(-10), // 最近10次记录
      currentMemory: this.monitorMemory()
    }
  }

  /**
   * 清理性能数据
   */
  clearMetrics() {
    this.metrics.renderTime = []
    this.metrics.apiTime = []
    this.metrics.memoryUsage = []
    this.timers.clear()
  }

  /**
   * 组件渲染优化包装器
   * @param {Function} renderFunc 渲染函数
   * @param {string} componentName 组件名称
   */
  wrapRender(renderFunc, componentName) {
    return (...args) => {
      this.timeStart(`render-${componentName}`)
      const result = renderFunc.apply(this, args)
      this.nextFrame(() => {
        this.timeEnd(`render-${componentName}`)
      })
      return result
    }
  }

  /**
   * 长列表优化
   * @param {Array} list 原始列表
   * @param {number} chunkSize 分块大小
   * @param {Function} callback 处理回调
   */
  processLargeList(list, chunkSize = 100, callback) {
    let index = 0
    
    const processChunk = () => {
      const chunk = list.slice(index, index + chunkSize)
      if (chunk.length > 0) {
        callback(chunk, index)
        index += chunkSize
        
        // 下一帧继续处理
        this.nextFrame(processChunk)
      }
    }
    
    processChunk()
  }

  /**
   * 资源预加载
   * @param {Array} urls 资源URL列表
   * @param {string} type 资源类型 ('image', 'script', 'style')
   */
  preloadResources(urls, type = 'image') {
    const promises = urls.map(url => {
      return new Promise((resolve, reject) => {
        let element
        
        switch (type) {
          case 'image':
            element = new Image()
            break
          case 'script':
            element = document.createElement('script')
            break
          case 'style':
            element = document.createElement('link')
            element.rel = 'stylesheet'
            break
          default:
            element = new Image()
        }
        
        element.onload = () => resolve(url)
        element.onerror = () => reject(new Error(`Failed to load ${url}`))
        element.src = url
        
        if (type === 'style') {
          element.href = url
        }
      })
    })
    
    return Promise.allSettled(promises)
  }
}

// 创建全局实例
const performanceOptimizer = new PerformanceOptimizer()

export default performanceOptimizer
