import{o as e,c as t,w as a,p as s,a as i,q as n,l,i as r,e as o,f as c,u as d,v as g,x as u,y as m,z as h,b as p,t as f,A as _,B as y,j as v,h as b,r as w,F as k,C as x,d as S,k as I,I as D,D as P}from"./index-DjruG92H.js";import{r as C,c as L}from"./request.j3WTH_xy.js";import{_ as O}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{p as M}from"./performance.o1AJnB7v.js";const N={getImagePage:e=>C.get(L.api.image.list,e),getImageById:e=>C.get(`${L.api.image.detail}/${e}`),getFactoryNames:()=>C.get(L.api.image.factories),getOrderNumbersByFactory:e=>C.get(L.api.image.orders,{factoryName:e}),countByFactory:e=>C.get(L.api.image.countByFactory,{factoryName:e}),countByOrder:e=>C.get(L.api.image.countByOrder,{orderNumber:e})};class z{static async detectPrinters(){try{return[{name:"默认打印机",value:"default",isDefault:!0},{name:"Microsoft Print to PDF",value:"pdf"},{name:"Microsoft XPS Document Writer",value:"xps"}]}catch(e){return[{name:"默认打印机",value:"default",isDefault:!0}]}}static async getPrinterStatus(e){try{return{name:e,status:"ready",paperLevel:"normal",inkLevel:"normal"}}catch(t){return{name:e,status:"unknown",paperLevel:"unknown",inkLevel:"unknown"}}}static preloadImages(e){return Promise.all(e.map((e=>new Promise(((t,a)=>{const s=new Image;s.onload=()=>t(s),s.onerror=()=>a(new Error(`图片加载失败: ${e}`)),s.src=e})))))}static generatePrintStyles(e){const{paperSize:t,quality:a,imagesPerPage:s}=e;return`\n      @page {\n        size: ${this.getPaperSizeCSS(t.value)};\n        margin: 20mm;\n      }\n      \n      body {\n        margin: 0;\n        padding: 0;\n        font-family: Arial, sans-serif;\n        -webkit-print-color-adjust: exact;\n        color-adjust: exact;\n      }\n      \n      .print-page {\n        page-break-after: always;\n        display: flex;\n        flex-wrap: wrap;\n        justify-content: center;\n        align-items: flex-start;\n        gap: 10px;\n        padding: 10px;\n        min-height: calc(100vh - 40mm);\n      }\n      \n      .print-page:last-child {\n        page-break-after: avoid;\n      }\n      \n      .image-container {\n        flex: 0 0 ${this.getImageContainerWidth(s.value)};\n        text-align: center;\n        margin-bottom: 15px;\n        break-inside: avoid;\n      }\n      \n      .print-image {\n        max-width: 100%;\n        max-height: ${this.getImageMaxHeight(s.value)};\n        object-fit: contain;\n        border: 1px solid #ddd;\n        border-radius: 4px;\n        ${this.getQualityCSS(a.value)}\n      }\n      \n      .image-info {\n        margin-top: 8px;\n        font-size: 11px;\n        color: #333;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n      \n      .image-name {\n        font-weight: bold;\n        margin-bottom: 4px;\n        font-size: 12px;\n      }\n      \n      .image-details {\n        font-size: 10px;\n        color: #666;\n        margin-bottom: 2px;\n      }\n      \n      @media print {\n        .print-page {\n          margin: 0;\n          padding: 5mm;\n        }\n        \n        .image-container {\n          margin-bottom: 10px;\n        }\n        \n        .image-info {\n          font-size: 10px;\n        }\n        \n        .image-name {\n          font-size: 11px;\n        }\n        \n        .image-details {\n          font-size: 9px;\n        }\n      }\n    `}static getPaperSizeCSS(e){return{A4:"A4",A3:"A3",Letter:"letter","4x6":"4in 6in"}[e]||"A4"}static getImageContainerWidth(e){return{1:"100%",2:"48%",4:"48%",6:"30%",9:"30%"}[e]||"48%"}static getImageMaxHeight(e){return{1:"700px",2:"450px",4:"320px",6:"220px",9:"160px"}[e]||"450px"}static getQualityCSS(e){return{draft:"image-rendering: pixelated;",normal:"image-rendering: auto;",high:"image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;",best:"image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;"}[e]||"image-rendering: auto;"}static formatDateTime(e){if(!e)return"";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(t){return e}}static generatePrintHTML(e,t){const a=t.imagesPerPage.value,s=this.generatePrintStyles(t);let i=`\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset="UTF-8">\n        <title>图片打印 - ${e.length}张图片</title>\n        <style>${s}</style>\n      </head>\n      <body>\n    `;for(let n=0;n<e.length;n+=a){const t=e.slice(n,n+a);i+='<div class="print-page">',t.forEach((e=>{i+=`\n          <div class="image-container">\n            <img class="print-image" src="${e.imageUrl}" alt="${e.imageName}" />\n            <div class="image-info">\n              <div class="image-name">${this.escapeHtml(e.imageName)}</div>\n              <div class="image-details">\n                工厂: ${this.escapeHtml(e.factoryName)} | 订单: ${this.escapeHtml(e.orderNumber)}\n              </div>\n              <div class="image-details">\n                上传时间: ${this.formatDateTime(e.uploadDate)}\n              </div>\n              <div class="image-details">\n                文件大小: ${e.fileSizeFormatted||"未知"}\n              </div>\n            </div>\n          </div>\n        `})),i+="</div>"}return i+="\n      </body>\n      </html>\n    ",i}static escapeHtml(e){if(!e)return"";const t=document.createElement("div");return t.textContent=e,t.innerHTML}static async executePrint(e,t){try{const a=e.map((e=>e.imageUrl));await this.preloadImages(a);const s=window.open("","_blank","width=800,height=600,scrollbars=yes");if(!s)throw new Error("无法打开打印窗口，请检查浏览器弹窗设置");const i=this.generatePrintHTML(e,t);return s.document.write(i),s.document.close(),new Promise(((e,t)=>{s.onload=()=>{setTimeout((()=>{try{s.print(),s.onafterprint=()=>{s.close(),e()},setTimeout((()=>{s.closed||s.close(),e()}),5e3)}catch(a){s.close(),t(a)}}),1e3)},s.onerror=e=>{s.close(),t(new Error("打印窗口加载失败: "+e))}}))}catch(a){throw new Error("打印失败: "+a.message)}}}const $=O({components:{SkeletonLoader:O({name:"SkeletonLoader",props:{type:{type:String,default:"text",validator:e=>["image","text","card","list-item","custom"].includes(e)},animated:{type:Boolean,default:!0},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"32rpx"},borderRadius:{type:[String,Number],default:"8rpx"}},computed:{imageStyle(){return{width:"number"==typeof this.width?this.width+"rpx":this.width,height:"number"==typeof this.height?this.height+"rpx":this.height,borderRadius:"number"==typeof this.borderRadius?this.borderRadius+"rpx":this.borderRadius}},textStyle(){return{width:"number"==typeof this.width?this.width+"rpx":this.width,height:"number"==typeof this.height?this.height+"rpx":this.height,borderRadius:"number"==typeof this.borderRadius?this.borderRadius+"rpx":this.borderRadius}}}},[["render",function(o,c,d,g,u,m){const h=r;return e(),t(h,{class:l(["skeleton-loader",{animated:d.animated}])},{default:a((()=>["image"===d.type?(e(),t(h,{key:0,class:"skeleton-image",style:s(m.imageStyle)},null,8,["style"])):"text"===d.type?(e(),t(h,{key:1,class:"skeleton-text",style:s(m.textStyle)},null,8,["style"])):"card"===d.type?(e(),t(h,{key:2,class:"skeleton-card"},{default:a((()=>[i(h,{class:"skeleton-image",style:{width:"100%",height:"200rpx"}}),i(h,{class:"skeleton-content"},{default:a((()=>[i(h,{class:"skeleton-text",style:{width:"80%",height:"32rpx"}}),i(h,{class:"skeleton-text",style:{width:"60%",height:"28rpx",marginTop:"16rpx"}})])),_:1})])),_:1})):"list-item"===d.type?(e(),t(h,{key:3,class:"skeleton-list-item"},{default:a((()=>[i(h,{class:"skeleton-avatar"}),i(h,{class:"skeleton-content"},{default:a((()=>[i(h,{class:"skeleton-text",style:{width:"70%",height:"32rpx"}}),i(h,{class:"skeleton-text",style:{width:"50%",height:"28rpx",marginTop:"12rpx"}})])),_:1})])),_:1})):(e(),t(h,{key:4,class:"skeleton-custom"},{default:a((()=>[n(o.$slots,"default",{},void 0,!0)])),_:3}))])),_:3},8,["class"])}],["__scopeId","data-v-ced8b12d"]])},data:()=>({showSearchForm:!0,viewMode:"grid",imageList:[],pageData:{current:1,size:12,total:0,pages:0},searchQuery:{factoryName:"",orderNumber:"",startDate:"",endDate:""},factoryOptions:[{label:"全部工厂",value:""}],factoryIndex:0,loading:!1,showPreview:!1,currentImage:{},imageScale:1,imageRotation:0,imageTransition:"transform 0.3s ease",minScale:.5,maxScale:3,wheelListenerAdded:!1,isDragging:!1,dragStartX:0,dragStartY:0,imageOffsetX:0,imageOffsetY:0,lastOffsetX:0,lastOffsetY:0,wheelListenerAdded:!1,imageLoadingStates:{},debounceTimer:null,virtualScroll:{enabled:!1,itemHeight:220,visibleCount:20,startIndex:0,endIndex:20},jumpPage:"",selectedImages:[],showPrintDialog:!1,printImages:[],printerIndex:0,printerOptions:[{name:"默认打印机",value:"default"},{name:"Microsoft Print to PDF",value:"pdf"},{name:"Microsoft XPS Document Writer",value:"xps"}],paperSizeIndex:0,paperSizeOptions:[{name:"A4 (210 × 297 mm)",value:"A4"},{name:"A3 (297 × 420 mm)",value:"A3"},{name:"Letter (216 × 279 mm)",value:"Letter"},{name:"4×6 英寸",value:"4x6"}],qualityIndex:1,qualityOptions:[{name:"草稿质量",value:"draft"},{name:"标准质量",value:"normal"},{name:"高质量",value:"high"},{name:"最佳质量",value:"best"}],imagesPerPageIndex:0,imagesPerPageOptions:[{name:"1张/页",value:1},{name:"2张/页",value:2},{name:"4张/页",value:4},{name:"6张/页",value:6},{name:"9张/页",value:9}]}),onLoad(){this.loadFactoryOptions(),this.loadImageList()},onShow(){C.clearCache("/image/factories"),this.loadFactoryOptions()},async mounted(){document.addEventListener("keydown",this.handleKeydown),this.addWheelListener(),await this.initPrinters()},beforeDestroy(){document.removeEventListener("keydown",this.handleKeydown),this.removeWheelListener()},onPullDownRefresh(){this.refreshList()},computed:{visibleImageList(){return!this.virtualScroll.enabled||this.imageList.length<=50?this.imageList:this.imageList.slice(this.virtualScroll.startIndex,this.virtualScroll.endIndex)},shouldUseVirtualScroll(){return this.imageList.length>100}},methods:{async loadFactoryOptions(){try{const e=(await N.getFactoryNames()).data||[];this.factoryOptions=[{label:"全部工厂",value:""},...e.map((e=>({label:e,value:e})))]}catch(e){}},async loadImageList(e=null){if(!this.loading){this.loading=!0;try{const t={current:e||this.pageData.current,size:this.pageData.size,...this.buildSearchParams()},a=await N.getImagePage(t),{data:s}=a;this.imageList=s.records,this.pageData=s,this.updateVirtualScroll()}catch(t){o({title:"加载失败",icon:"none"})}finally{this.loading=!1,c()}}},updateVirtualScroll(){this.shouldUseVirtualScroll?(this.virtualScroll.enabled=!0,this.virtualScroll.endIndex=Math.min(this.virtualScroll.visibleCount,this.imageList.length)):this.virtualScroll.enabled=!1},debounceSearch:M.debounce((function(){this.handleSearch()}),300),buildSearchParams(){const e={};return this.searchQuery.factoryName&&(e.factoryName=this.searchQuery.factoryName),this.searchQuery.orderNumber&&(e.orderNumber=this.searchQuery.orderNumber),this.searchQuery.startDate&&(e.startDate=this.searchQuery.startDate),this.searchQuery.endDate&&(e.endDate=this.searchQuery.endDate),e},toggleSearchForm(){this.showSearchForm=!this.showSearchForm},onFactoryChange(e){this.factoryIndex=e.detail.value,this.searchQuery.factoryName=this.factoryOptions[this.factoryIndex].value},onStartDateChange(e){this.searchQuery.startDate=e.detail.value},onEndDateChange(e){this.searchQuery.endDate=e.detail.value},handleSearch(){this.showSearchForm=!1,this.refreshList()},resetSearch(){this.searchQuery={factoryName:"",orderNumber:"",imageName:"",startDate:"",endDate:""},this.factoryIndex=0,this.refreshList()},refreshList(){this.pageData.current=1,this.loadImageList()},goToPage(e){e<1||e>this.pageData.pages||e===this.pageData.current||this.loading||(this.pageData.current=e,this.loadImageList(e),d({scrollTop:0,duration:300}))},getPageNumbers(){const e=this.pageData.current,t=this.pageData.pages,a=[],s=g().windowWidth<768;if(t<=(s?5:7))for(let i=1;i<=t;i++)a.push(i);else{const i=s?3:5,n=Math.floor(i/2);if(e<=n+1){for(let e=1;e<=i;e++)a.push(e);a.push("..."),a.push(t)}else if(e>=t-n){a.push(1),a.push("...");for(let e=t-i+1;e<=t;e++)a.push(e)}else{a.push(1),a.push("...");for(let t=e-n;t<=e+n;t++)a.push(t);a.push("..."),a.push(t)}}return a},onJumpPageInput(e){this.jumpPage=e.detail.value},quickJump(){const e=parseInt(this.jumpPage);e&&e>=1&&e<=this.pageData.pages?(this.goToPage(e),this.jumpPage=""):o({title:`请输入1-${this.pageData.pages}之间的页码`,icon:"none"})},setViewMode(e){this.viewMode=e},previewImage(e){this.currentImage=e,this.showPreview=!0,this.resetImageControls(),this.$nextTick((()=>{this.addWheelListener()}))},closePreview(){this.removeWheelListener(),this.showPreview=!1,this.currentImage={},this.resetImageControls()},resetImageControls(){this.imageScale=1,this.imageRotation=0,this.imageTransition="transform 0.3s ease",this.imageOffsetX=0,this.imageOffsetY=0,this.lastOffsetX=0,this.lastOffsetY=0,this.isDragging=!1},rotateLeft(){this.imageRotation-=90},rotateRight(){this.imageRotation+=90},zoomIn(e=.2){this.imageScale<this.maxScale&&(this.imageScale=Math.min(this.imageScale+e,this.maxScale))},zoomOut(e=.2){this.imageScale>this.minScale&&(this.imageScale=Math.max(this.imageScale-e,this.minScale),this.imageScale<=1&&(this.imageOffsetX=0,this.imageOffsetY=0,this.lastOffsetX=0,this.lastOffsetY=0))},resetImage(){this.resetImageControls()},startDrag(e){e.preventDefault(),this.isDragging=!0,this.dragStartX=e.clientX,this.dragStartY=e.clientY,this.lastOffsetX=this.imageOffsetX,this.lastOffsetY=this.imageOffsetY},onDrag(e){if(!this.isDragging)return;e.preventDefault();const t=e.clientX-this.dragStartX,a=e.clientY-this.dragStartY;this.imageOffsetX=this.lastOffsetX+t,this.imageOffsetY=this.lastOffsetY+a},endDrag(){this.isDragging=!1},toggleZoom(){this.imageTransition="transform 0.3s ease",1===this.imageScale?this.imageScale=2:(this.imageScale=1,this.imageOffsetX=0,this.imageOffsetY=0,this.lastOffsetX=0,this.lastOffsetY=0)},onImageLoad(){},onPreviewImageLoad(){},addWheelListener(){this.$nextTick((()=>{const e=document.querySelector(".preview-image-wrapper");e&&this.showPreview&&(this.removeWheelListener(),e.addEventListener("wheel",this.handleWheelNative,{passive:!1}),e.addEventListener("mousewheel",this.handleWheelNative,{passive:!1}),e.addEventListener("DOMMouseScroll",this.handleWheelNative,{passive:!1}),this.wheelListenerAdded=!0)}))},removeWheelListener(){if(this.wheelListenerAdded){const e=document.querySelector(".preview-image-wrapper");e&&(e.removeEventListener("wheel",this.handleWheelNative),e.removeEventListener("mousewheel",this.handleWheelNative),e.removeEventListener("DOMMouseScroll",this.handleWheelNative),this.wheelListenerAdded=!1)}},handleWheelNative(e){if(!this.showPreview)return;e.preventDefault(),e.stopPropagation();let t=0;void 0!==e.deltaY?t=-e.deltaY:void 0!==e.wheelDelta?t=e.wheelDelta:void 0!==e.detail&&(t=40*-e.detail),0!==t&&(t>0?this.zoomIn(.1):this.zoomOut(.1))},handleKeydown(e){if(this.showPreview)switch(e.key){case"Escape":this.closePreview();break;case"ArrowLeft":e.preventDefault(),this.rotateLeft();break;case"ArrowRight":e.preventDefault(),this.rotateRight();break;case"0":e.preventDefault(),this.resetImage()}},onImageError(){},toggleImageSelection(e){const t=this.selectedImages.findIndex((t=>t.id===e.id));t>-1?this.selectedImages.splice(t,1):this.selectedImages.push(e)},isImageSelected(e){return this.selectedImages.some((t=>t.id===e))},clearSelection(){this.selectedImages=[]},showPrintModal(){0!==this.selectedImages.length?(this.printImages=[...this.selectedImages],this.showPrintDialog=!0):o({title:"请先选择要打印的图片",icon:"none"})},printSingleImage(){this.printImages=[this.currentImage],this.showPrintDialog=!0},closePrintModal(){this.showPrintDialog=!1,this.printImages=[]},onPrinterChange(e){this.printerIndex=e.detail.value},onPaperSizeChange(e){this.paperSizeIndex=e.detail.value},onQualityChange(e){this.qualityIndex=e.detail.value},onImagesPerPageChange(e){this.imagesPerPageIndex=e.detail.value},async confirmPrint(){try{u({title:"准备打印..."});const e={printer:this.printerOptions[this.printerIndex],paperSize:this.paperSizeOptions[this.paperSizeIndex],quality:this.qualityOptions[this.qualityIndex],imagesPerPage:this.imagesPerPageOptions[this.imagesPerPageIndex]};await this.executePrint(this.printImages,e),m(),o({title:"打印任务已发送",icon:"success"}),this.closePrintModal(),this.clearSelection()}catch(e){m(),o({title:"打印失败: "+e.message,icon:"none"})}},async initPrinters(){try{const e=await z.detectPrinters();this.printerOptions=e}catch(e){}},async executePrint(e,t){await z.executePrint(e,t)},formatDate(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},formatDateTime(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`}}},[["render",function(n,o,c,d,g,u){const m=S,C=r,L=I,O=D,M=h("SkeletonLoader"),N=P;return e(),t(C,{class:"container"},{default:a((()=>[i(C,{class:"search-section"},{default:a((()=>[i(C,{class:"search-card"},{default:a((()=>[i(C,{class:"search-title"},{default:a((()=>[i(m,{class:"title-text"},{default:a((()=>[p("图片检索")])),_:1}),i(C,{class:"toggle-btn",onClick:u.toggleSearchForm},{default:a((()=>[i(m,{class:"toggle-text"},{default:a((()=>[p(f(g.showSearchForm?"收起":"展开"),1)])),_:1}),i(m,{class:"toggle-icon"},{default:a((()=>[p(f(g.showSearchForm?"▲":"▼"),1)])),_:1})])),_:1},8,["onClick"])])),_:1}),_(i(C,{class:"search-form"},{default:a((()=>[i(C,{class:"search-filters-row"},{default:a((()=>[i(C,{class:"form-row"},{default:a((()=>[i(m,{class:"form-label"},{default:a((()=>[p("工厂名称")])),_:1}),i(L,{value:g.factoryIndex,range:g.factoryOptions,"range-key":"label",onChange:u.onFactoryChange},{default:a((()=>[i(C,{class:"picker-input"},{default:a((()=>[i(m,{class:"picker-text"},{default:a((()=>[p(f(g.factoryOptions[g.factoryIndex].label),1)])),_:1}),i(m,{class:"picker-arrow"},{default:a((()=>[p("▼")])),_:1})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1}),i(C,{class:"form-row"},{default:a((()=>[i(m,{class:"form-label"},{default:a((()=>[p("订单号")])),_:1}),i(O,{class:"form-input",placeholder:"请输入订单号",modelValue:g.searchQuery.orderNumber,"onUpdate:modelValue":o[0]||(o[0]=e=>g.searchQuery.orderNumber=e)},null,8,["modelValue"])])),_:1}),i(C,{class:"form-row"},{default:a((()=>[i(m,{class:"form-label"},{default:a((()=>[p("时间范围")])),_:1}),i(C,{class:"date-range"},{default:a((()=>[i(L,{mode:"date",value:g.searchQuery.startDate,onChange:u.onStartDateChange},{default:a((()=>[i(C,{class:"date-input"},{default:a((()=>[i(m,{class:"date-text"},{default:a((()=>[p(f(g.searchQuery.startDate||"开始日期"),1)])),_:1})])),_:1})])),_:1},8,["value","onChange"]),i(m,{class:"date-separator"},{default:a((()=>[p("至")])),_:1}),i(L,{mode:"date",value:g.searchQuery.endDate,onChange:u.onEndDateChange},{default:a((()=>[i(C,{class:"date-input"},{default:a((()=>[i(m,{class:"date-text"},{default:a((()=>[p(f(g.searchQuery.endDate||"结束日期"),1)])),_:1})])),_:1})])),_:1},8,["value","onChange"])])),_:1})])),_:1})])),_:1}),i(C,{class:"search-actions"},{default:a((()=>[i(C,{class:"action-btn reset-btn",onClick:u.resetSearch},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("重置")])),_:1})])),_:1},8,["onClick"]),i(C,{class:"action-btn search-btn",onClick:u.handleSearch},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("搜索")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1},512),[[y,g.showSearchForm]])])),_:1})])),_:1}),i(C,{class:"stats-bar"},{default:a((()=>[i(C,{class:"stats-info"},{default:a((()=>[i(m,{class:"stats-text"},{default:a((()=>[p("共找到 "+f(g.pageData.total)+" 张图片",1)])),_:1}),g.pageData.total>0?(e(),t(m,{key:0,class:"page-stats"},{default:a((()=>[p(" 显示第 "+f((g.pageData.current-1)*g.pageData.size+1)+"-"+f(Math.min(g.pageData.current*g.pageData.size,g.pageData.total))+" 张 ",1)])),_:1})):v("",!0)])),_:1}),i(C,{class:"action-buttons"},{default:a((()=>[g.selectedImages.length>0?(e(),t(C,{key:0,class:"batch-actions"},{default:a((()=>[i(m,{class:"selected-count"},{default:a((()=>[p("已选择 "+f(g.selectedImages.length)+" 张",1)])),_:1}),i(C,{class:"action-btn print-btn",onClick:u.showPrintModal},{default:a((()=>[i(m,{class:"btn-icon"},{default:a((()=>[p("🖨️")])),_:1}),i(m,{class:"btn-text"},{default:a((()=>[p("批量打印")])),_:1})])),_:1},8,["onClick"]),i(C,{class:"action-btn clear-btn",onClick:u.clearSelection},{default:a((()=>[i(m,{class:"btn-icon"},{default:a((()=>[p("✕")])),_:1}),i(m,{class:"btn-text"},{default:a((()=>[p("清空选择")])),_:1})])),_:1},8,["onClick"])])),_:1})):v("",!0),i(C,{class:"view-mode"},{default:a((()=>[i(C,{class:l(["mode-btn",{active:"grid"===g.viewMode}]),onClick:o[1]||(o[1]=e=>u.setViewMode("grid"))},{default:a((()=>[i(m,{class:"mode-icon"},{default:a((()=>[p("⊞")])),_:1})])),_:1},8,["class"]),i(C,{class:l(["mode-btn",{active:"list"===g.viewMode}]),onClick:o[2]||(o[2]=e=>u.setViewMode("list"))},{default:a((()=>[i(m,{class:"mode-icon"},{default:a((()=>[p("☰")])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})])),_:1}),g.loading&&0===g.imageList.length?(e(),t(C,{key:0,class:"skeleton-container"},{default:a((()=>[i(C,{class:l(["image-list",g.viewMode])},{default:a((()=>[(e(),b(k,null,w(12,(e=>i(M,{key:e,type:"card",class:"skeleton-item"}))),64))])),_:1},8,["class"])])),_:1})):(e(),t(C,{key:1,class:l(["image-list",g.viewMode])},{default:a((()=>[(e(!0),b(k,null,w(u.visibleImageList,(s=>(e(),t(C,{class:l(["image-item",{selected:u.isImageSelected(s.id)}]),key:s.id},{default:a((()=>[i(C,{class:"image-checkbox",onClick:x((e=>u.toggleImageSelection(s)),["stop"])},{default:a((()=>[i(C,{class:l(["checkbox",{checked:u.isImageSelected(s.id)}])},{default:a((()=>[u.isImageSelected(s.id)?(e(),t(m,{key:0,class:"check-icon"},{default:a((()=>[p("✓")])),_:1})):v("",!0)])),_:2},1032,["class"])])),_:2},1032,["onClick"]),i(C,{class:"image-wrapper",onClick:e=>u.previewImage(s)},{default:a((()=>[i(N,{class:"image-thumb",src:s.imageUrl,mode:"aspectFill","lazy-load":!0,"show-loading":!0,"show-error":!0,onLoad:u.onImageLoad,onError:u.onImageError},null,8,["src","onLoad","onError"]),i(C,{class:"image-overlay"},{default:a((()=>[i(m,{class:"overlay-icon"},{default:a((()=>[p("🔍")])),_:1})])),_:1})])),_:2},1032,["onClick"]),i(C,{class:"image-info"},{default:a((()=>[i(m,{class:"image-name"},{default:a((()=>[p(f(s.imageName),1)])),_:2},1024),i(m,{class:"image-factory"},{default:a((()=>[p(f(s.factoryName),1)])),_:2},1024),i(m,{class:"image-order"},{default:a((()=>[p(f(s.orderNumber),1)])),_:2},1024),i(m,{class:"image-size"},{default:a((()=>[p(f(s.fileSizeFormatted),1)])),_:2},1024),i(m,{class:"image-date"},{default:a((()=>[p(f(u.formatDate(s.uploadDate)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1},8,["class"])),g.pageData.pages>1?(e(),t(C,{key:2,class:"pagination"},{default:a((()=>[i(C,{class:"pagination-info"},{default:a((()=>[i(m,{class:"page-info"},{default:a((()=>[p("第 "+f(g.pageData.current)+" 页，共 "+f(g.pageData.pages)+" 页",1)])),_:1})])),_:1}),i(C,{class:"pagination-controls"},{default:a((()=>[i(C,{class:l(["page-btn",{disabled:1===g.pageData.current}]),onClick:o[3]||(o[3]=e=>u.goToPage(1))},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("首页")])),_:1})])),_:1},8,["class"]),i(C,{class:l(["page-btn",{disabled:1===g.pageData.current}]),onClick:o[4]||(o[4]=e=>u.goToPage(g.pageData.current-1))},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("上一页")])),_:1})])),_:1},8,["class"]),i(C,{class:"page-numbers"},{default:a((()=>[(e(!0),b(k,null,w(u.getPageNumbers(),(s=>(e(),t(C,{key:s,class:l(["page-number",{active:s===g.pageData.current,ellipsis:"..."===s}]),onClick:e=>"..."!==s?u.goToPage(s):null},{default:a((()=>[i(m,{class:"number-text"},{default:a((()=>[p(f(s),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),i(C,{class:l(["page-btn",{disabled:g.pageData.current===g.pageData.pages}]),onClick:o[5]||(o[5]=e=>u.goToPage(g.pageData.current+1))},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("下一页")])),_:1})])),_:1},8,["class"]),i(C,{class:l(["page-btn",{disabled:g.pageData.current===g.pageData.pages}]),onClick:o[6]||(o[6]=e=>u.goToPage(g.pageData.pages))},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("末页")])),_:1})])),_:1},8,["class"])])),_:1}),g.pageData.pages>10?(e(),t(C,{key:0,class:"quick-jump"},{default:a((()=>[i(m,{class:"jump-label"},{default:a((()=>[p("跳转到")])),_:1}),i(O,{class:"jump-input",type:"number",value:g.jumpPage,onInput:u.onJumpPageInput,onConfirm:u.quickJump,placeholder:"页码",max:g.pageData.pages,min:1},null,8,["value","onInput","onConfirm","max"]),i(m,{class:"jump-label"},{default:a((()=>[p("页")])),_:1}),i(C,{class:"jump-btn",onClick:u.quickJump},{default:a((()=>[i(m,{class:"jump-text"},{default:a((()=>[p("跳转")])),_:1})])),_:1},8,["onClick"])])),_:1})):v("",!0)])),_:1})):v("",!0),g.loading||0!==g.imageList.length?v("",!0):(e(),t(C,{key:3,class:"empty-state"},{default:a((()=>[i(m,{class:"empty-icon"},{default:a((()=>[p("🖼️")])),_:1}),i(m,{class:"empty-text"},{default:a((()=>[p("暂无图片数据")])),_:1}),i(m,{class:"empty-desc"},{default:a((()=>[p("请调整搜索条件后重试")])),_:1})])),_:1})),g.loading?(e(),t(C,{key:4,class:"loading-state"},{default:a((()=>[i(C,{class:"loading-spinner"}),i(m,{class:"loading-text"},{default:a((()=>[p("加载中...")])),_:1})])),_:1})):v("",!0),g.showPreview?(e(),t(C,{key:5,class:"preview-modal",onClick:u.closePreview},{default:a((()=>[i(C,{class:"preview-content",onClick:o[9]||(o[9]=x((()=>{}),["stop"]))},{default:a((()=>[i(C,{class:"preview-header"},{default:a((()=>[i(m,{class:"preview-title"},{default:a((()=>[p(f(g.currentImage.imageName),1)])),_:1}),i(C,{class:"preview-controls"},{default:a((()=>[i(C,{class:"control-btn",onClick:u.rotateLeft,title:"向左旋转"},{default:a((()=>[i(m,{class:"control-icon"},{default:a((()=>[p("↺")])),_:1})])),_:1},8,["onClick"]),i(C,{class:"control-btn",onClick:u.rotateRight,title:"向右旋转"},{default:a((()=>[i(m,{class:"control-icon"},{default:a((()=>[p("↻")])),_:1})])),_:1},8,["onClick"]),i(C,{class:"control-btn",onClick:o[7]||(o[7]=e=>u.zoomOut(.1)),title:"缩小"},{default:a((()=>[i(m,{class:"control-icon"},{default:a((()=>[p("-")])),_:1})])),_:1}),i(C,{class:"control-btn",onClick:o[8]||(o[8]=e=>u.zoomIn(.1)),title:"放大"},{default:a((()=>[i(m,{class:"control-icon"},{default:a((()=>[p("+")])),_:1})])),_:1}),i(C,{class:"control-btn",onClick:u.resetImage,title:"重置"},{default:a((()=>[i(m,{class:"control-icon"},{default:a((()=>[p("⟲")])),_:1})])),_:1},8,["onClick"]),i(C,{class:"control-btn print-single-btn",onClick:u.printSingleImage,title:"打印"},{default:a((()=>[i(m,{class:"control-icon"},{default:a((()=>[p("🖨️")])),_:1})])),_:1},8,["onClick"])])),_:1}),i(C,{class:"close-btn",onClick:u.closePreview},{default:a((()=>[i(m,{class:"close-icon"},{default:a((()=>[p("✕")])),_:1})])),_:1},8,["onClick"])])),_:1}),i(C,{class:"preview-image-wrapper",onMousedown:u.startDrag,onMousemove:u.onDrag,onMouseup:u.endDrag,onMouseleave:u.endDrag,onDblclick:u.toggleZoom,ref:"imageWrapper",style:s({cursor:g.isDragging?"grabbing":"grab"})},{default:a((()=>[i(C,{class:"image-container",style:s({transform:`translate(${g.imageOffsetX}px, ${g.imageOffsetY}px) scale(${g.imageScale}) rotate(${g.imageRotation}deg)`,transition:g.isDragging?"none":g.imageTransition})},{default:a((()=>[i(N,{class:"preview-image",src:g.currentImage.imageUrl,mode:"aspectFit",onLoad:u.onPreviewImageLoad},null,8,["src","onLoad"])])),_:1},8,["style"])])),_:1},8,["onMousedown","onMousemove","onMouseup","onMouseleave","onDblclick","style"]),i(C,{class:"preview-info"},{default:a((()=>[i(C,{class:"info-row"},{default:a((()=>[i(C,{class:"info-item"},{default:a((()=>[i(m,{class:"info-label"},{default:a((()=>[p("工厂：")])),_:1}),i(m,{class:"info-value"},{default:a((()=>[p(f(g.currentImage.factoryName),1)])),_:1})])),_:1}),i(C,{class:"info-item"},{default:a((()=>[i(m,{class:"info-label"},{default:a((()=>[p("订单号：")])),_:1}),i(m,{class:"info-value"},{default:a((()=>[p(f(g.currentImage.orderNumber),1)])),_:1})])),_:1})])),_:1}),i(C,{class:"info-row"},{default:a((()=>[i(C,{class:"info-item"},{default:a((()=>[i(m,{class:"info-label"},{default:a((()=>[p("文件大小：")])),_:1}),i(m,{class:"info-value"},{default:a((()=>[p(f(g.currentImage.fileSizeFormatted),1)])),_:1})])),_:1}),i(C,{class:"info-item"},{default:a((()=>[i(m,{class:"info-label"},{default:a((()=>[p("上传时间：")])),_:1}),i(m,{class:"info-value"},{default:a((()=>[p(f(u.formatDateTime(g.currentImage.uploadDate)),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["onClick"])):v("",!0),g.showPrintDialog?(e(),t(C,{key:6,class:"print-modal",onClick:u.closePrintModal},{default:a((()=>[i(C,{class:"print-content",onClick:o[10]||(o[10]=x((()=>{}),["stop"]))},{default:a((()=>[i(C,{class:"print-header"},{default:a((()=>[i(m,{class:"print-title"},{default:a((()=>[p("打印设置")])),_:1}),i(C,{class:"close-btn",onClick:u.closePrintModal},{default:a((()=>[i(m,{class:"close-icon"},{default:a((()=>[p("✕")])),_:1})])),_:1},8,["onClick"])])),_:1}),i(C,{class:"print-body"},{default:a((()=>[i(C,{class:"print-preview"},{default:a((()=>[i(m,{class:"preview-title"},{default:a((()=>[p("打印预览 ("+f(g.printImages.length)+" 张图片)",1)])),_:1}),i(C,{class:"preview-grid"},{default:a((()=>[(e(!0),b(k,null,w(g.printImages.slice(0,6),(s=>(e(),t(C,{class:"preview-item",key:s.id},{default:a((()=>[i(N,{class:"preview-thumb",src:s.imageUrl,mode:"aspectFit"},null,8,["src"]),i(m,{class:"preview-name"},{default:a((()=>[p(f(s.imageName),1)])),_:2},1024)])),_:2},1024)))),128)),g.printImages.length>6?(e(),t(C,{key:0,class:"preview-more"},{default:a((()=>[i(m,{class:"more-text"},{default:a((()=>[p("还有 "+f(g.printImages.length-6)+" 张...",1)])),_:1})])),_:1})):v("",!0)])),_:1})])),_:1}),i(C,{class:"print-settings"},{default:a((()=>[i(C,{class:"setting-group"},{default:a((()=>[i(m,{class:"setting-label"},{default:a((()=>[p("打印机")])),_:1}),i(L,{value:g.printerIndex,range:g.printerOptions,"range-key":"name",onChange:u.onPrinterChange},{default:a((()=>[i(C,{class:"picker-input"},{default:a((()=>[i(m,{class:"picker-text"},{default:a((()=>[p(f(g.printerOptions[g.printerIndex].name),1)])),_:1}),i(m,{class:"picker-arrow"},{default:a((()=>[p("▼")])),_:1})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1}),i(C,{class:"setting-group"},{default:a((()=>[i(m,{class:"setting-label"},{default:a((()=>[p("纸张大小")])),_:1}),i(L,{value:g.paperSizeIndex,range:g.paperSizeOptions,"range-key":"name",onChange:u.onPaperSizeChange},{default:a((()=>[i(C,{class:"picker-input"},{default:a((()=>[i(m,{class:"picker-text"},{default:a((()=>[p(f(g.paperSizeOptions[g.paperSizeIndex].name),1)])),_:1}),i(m,{class:"picker-arrow"},{default:a((()=>[p("▼")])),_:1})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1}),i(C,{class:"setting-group"},{default:a((()=>[i(m,{class:"setting-label"},{default:a((()=>[p("打印质量")])),_:1}),i(L,{value:g.qualityIndex,range:g.qualityOptions,"range-key":"name",onChange:u.onQualityChange},{default:a((()=>[i(C,{class:"picker-input"},{default:a((()=>[i(m,{class:"picker-text"},{default:a((()=>[p(f(g.qualityOptions[g.qualityIndex].name),1)])),_:1}),i(m,{class:"picker-arrow"},{default:a((()=>[p("▼")])),_:1})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1}),i(C,{class:"setting-group"},{default:a((()=>[i(m,{class:"setting-label"},{default:a((()=>[p("每页图片数")])),_:1}),i(L,{value:g.imagesPerPageIndex,range:g.imagesPerPageOptions,"range-key":"name",onChange:u.onImagesPerPageChange},{default:a((()=>[i(C,{class:"picker-input"},{default:a((()=>[i(m,{class:"picker-text"},{default:a((()=>[p(f(g.imagesPerPageOptions[g.imagesPerPageIndex].name),1)])),_:1}),i(m,{class:"picker-arrow"},{default:a((()=>[p("▼")])),_:1})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1})])),_:1})])),_:1}),i(C,{class:"print-footer"},{default:a((()=>[i(C,{class:"footer-btn cancel-btn",onClick:u.closePrintModal},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("取消")])),_:1})])),_:1},8,["onClick"]),i(C,{class:"footer-btn confirm-btn",onClick:u.confirmPrint},{default:a((()=>[i(m,{class:"btn-text"},{default:a((()=>[p("开始打印")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["onClick"])):v("",!0)])),_:1})}],["__scopeId","data-v-b88c7abd"]]);export{$ as default};
