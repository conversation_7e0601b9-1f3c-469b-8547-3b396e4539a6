/**
 * 前端性能优化配置
 */

export default {
  // 网络请求优化
  request: {
    // 请求缓存配置
    cache: {
      enabled: true,
      maxSize: 100,
      timeout: 5 * 60 * 1000 // 5分钟
    },
    
    // 请求防抖配置
    debounce: {
      enabled: true,
      delay: 300 // 300ms
    },
    
    // 请求重试配置
    retry: {
      enabled: true,
      maxRetries: 3,
      retryDelay: 1000
    }
  },

  // 图片优化配置
  image: {
    // 懒加载配置
    lazyLoad: {
      enabled: true,
      threshold: 100, // 提前100px开始加载
      placeholder: '/static/images/placeholder.png'
    },
    
    // 预加载配置
    preload: {
      enabled: false, // 暂时禁用，避免过度优化
      maxConcurrent: 3,
      priority: ['thumbnail', 'medium', 'large']
    },
    
    // 压缩配置
    compression: {
      enabled: true,
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1080
    }
  },

  // 列表渲染优化
  list: {
    // 虚拟滚动配置
    virtualScroll: {
      enabled: true,
      threshold: 100, // 超过100项启用虚拟滚动
      itemHeight: 220, // 每项高度(rpx)
      overscan: 5 // 预渲染项数
    },
    
    // 分页配置
    pagination: {
      pageSize: 20,
      maxPages: 10 // 最多缓存10页数据
    }
  },

  // 组件渲染优化
  component: {
    // 防抖配置
    debounce: {
      search: 300,
      input: 200,
      scroll: 16 // 约60fps
    },
    
    // 节流配置
    throttle: {
      resize: 100,
      scroll: 16
    }
  },

  // 内存管理
  memory: {
    // 缓存清理配置
    cleanup: {
      enabled: true,
      interval: 5 * 60 * 1000, // 5分钟清理一次
      maxMemoryUsage: 100 * 1024 * 1024 // 100MB
    },
    
    // 监控配置
    monitoring: {
      enabled: true,
      interval: 30 * 1000, // 30秒监控一次
      alertThreshold: 80 * 1024 * 1024 // 80MB告警
    }
  },

  // 骨架屏配置
  skeleton: {
    enabled: true,
    animation: true,
    minLoadingTime: 500 // 最小显示时间，避免闪烁
  },

  // 错误处理配置
  error: {
    // 重试配置
    retry: {
      enabled: true,
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true
    },
    
    // 降级配置
    fallback: {
      enabled: true,
      showErrorBoundary: true,
      defaultErrorMessage: '加载失败，请重试'
    }
  },

  // 开发环境配置
  development: {
    // 性能监控
    performance: {
      enabled: true,
      logLevel: 'debug',
      showMetrics: true
    },
    
    // 调试配置
    debug: {
      enabled: true,
      showRequestLogs: true,
      showRenderTime: true
    }
  },

  // 生产环境配置
  production: {
    // 性能监控
    performance: {
      enabled: true,
      logLevel: 'error',
      showMetrics: false
    },
    
    // 调试配置
    debug: {
      enabled: false,
      showRequestLogs: false,
      showRenderTime: false
    },
    
    // 优化配置
    optimization: {
      minifyCode: true,
      removeConsole: true,
      enableGzip: true
    }
  }
}
