package com.finance.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 图片访问优化服务
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
public class ImageAccessService {

    @Value("${finance.server.host:localhost}")
    private String serverHost;
    
    @Value("${server.port:8080}")
    private String serverPort;
    
    @Value("${finance.image.cdn.enabled:false}")
    private boolean cdnEnabled;
    
    @Value("${finance.image.cdn.domain:}")
    private String cdnDomain;
    
    @Value("${finance.image.webp.enabled:true}")
    private boolean webpEnabled;
    
    @Value("${finance.image.cache.enabled:true}")
    private boolean cacheEnabled;

    /**
     * 获取优化的图片URL
     * 
     * @param imagePath 图片路径
     * @param size 图片尺寸 (small, medium, large, original)
     * @param request HTTP请求对象，用于检测浏览器支持
     * @return 优化后的图片URL
     */
    @Cacheable(value = "imageUrls", key = "#imagePath + '_' + #size", condition = "#cacheEnabled")
    public String getOptimizedImageUrl(String imagePath, String size, HttpServletRequest request) {
        if (imagePath == null || imagePath.isEmpty()) {
            return null;
        }
        
        // 1. 确定基础URL
        String baseUrl = getBaseUrl();
        
        // 2. 处理图片路径
        String normalizedPath = normalizeImagePath(imagePath);
        
        // 3. 添加尺寸参数
        String sizedPath = addSizeParameter(normalizedPath, size);
        
        // 4. 检查WebP支持
        if (webpEnabled && supportsWebP(request)) {
            sizedPath = addWebPParameter(sizedPath);
        }
        
        // 5. 添加缓存参数
        String finalUrl = baseUrl + sizedPath;
        if (cacheEnabled) {
            finalUrl = addCacheParameters(finalUrl);
        }
        
        log.debug("生成优化图片URL: {} -> {}", imagePath, finalUrl);
        return finalUrl;
    }
    
    /**
     * 获取图片的多种尺寸URL
     * 
     * @param imagePath 图片路径
     * @param request HTTP请求对象
     * @return 包含不同尺寸URL的Map
     */
    @Cacheable(value = "imageUrlSets", key = "#imagePath", condition = "#cacheEnabled")
    public Map<String, String> getImageUrlSet(String imagePath, HttpServletRequest request) {
        Map<String, String> urlSet = new HashMap<>();
        
        urlSet.put("thumbnail", getOptimizedImageUrl(imagePath, "small", request));
        urlSet.put("medium", getOptimizedImageUrl(imagePath, "medium", request));
        urlSet.put("large", getOptimizedImageUrl(imagePath, "large", request));
        urlSet.put("original", getOptimizedImageUrl(imagePath, "original", request));
        
        return urlSet;
    }
    
    /**
     * 预加载图片URL
     * 
     * @param imagePaths 图片路径列表
     * @param size 图片尺寸
     * @param request HTTP请求对象
     * @return 预加载的URL列表
     */
    public Map<String, String> preloadImageUrls(String[] imagePaths, String size, HttpServletRequest request) {
        Map<String, String> preloadUrls = new HashMap<>();
        
        for (String imagePath : imagePaths) {
            String url = getOptimizedImageUrl(imagePath, size, request);
            if (url != null) {
                preloadUrls.put(imagePath, url);
            }
        }
        
        return preloadUrls;
    }
    
    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        if (cdnEnabled && !cdnDomain.isEmpty()) {
            return cdnDomain.endsWith("/") ? cdnDomain : cdnDomain + "/";
        }
        
        return "http://" + serverHost + ":" + serverPort + "/api/images/";
    }
    
    /**
     * 标准化图片路径
     */
    private String normalizeImagePath(String imagePath) {
        // 确保路径使用正斜杠
        String normalizedPath = imagePath.replace("\\", "/");
        
        // 确保路径不以斜杠开头
        if (normalizedPath.startsWith("/")) {
            normalizedPath = normalizedPath.substring(1);
        }
        
        return normalizedPath;
    }
    
    /**
     * 添加尺寸参数
     */
    private String addSizeParameter(String imagePath, String size) {
        if (size == null || size.equals("original")) {
            return imagePath;
        }
        
        // 在URL中添加尺寸参数
        String separator = imagePath.contains("?") ? "&" : "?";
        return imagePath + separator + "size=" + size;
    }
    
    /**
     * 添加WebP参数
     */
    private String addWebPParameter(String imagePath) {
        String separator = imagePath.contains("?") ? "&" : "?";
        return imagePath + separator + "format=webp";
    }
    
    /**
     * 添加缓存参数
     */
    private String addCacheParameters(String url) {
        String separator = url.contains("?") ? "&" : "?";
        return url + separator + "cache=1&v=" + System.currentTimeMillis() / (1000 * 60 * 60); // 按小时更新
    }
    
    /**
     * 检查浏览器是否支持WebP
     */
    private boolean supportsWebP(HttpServletRequest request) {
        if (request == null) {
            return false;
        }
        
        String acceptHeader = request.getHeader("Accept");
        return acceptHeader != null && acceptHeader.contains("image/webp");
    }
    
    /**
     * 清除图片URL缓存
     * 
     * @param imagePath 图片路径
     */
    public void clearImageUrlCache(String imagePath) {
        // 这里可以集成Spring Cache的清除逻辑
        log.info("清除图片URL缓存: {}", imagePath);
    }
}
