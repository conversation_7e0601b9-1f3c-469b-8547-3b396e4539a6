/**
 * 工厂管理API
 */
import request from '@/utils/request.js'
import config from '@/config/api.js'

export default {
  /**
   * 分页查询工厂列表
   */
  getFactoryPage(params) {
    return request.get(config.api.factory.list, params)
  },

  /**
   * 分页查询工厂列表（管理界面，包含密码信息）
   */
  getFactoryPageAdmin(params) {
    return request.get(config.api.factory.listAdmin, params)
  },

  /**
   * 根据ID获取工厂信息
   */
  getFactoryById(id) {
    return request.get(`${config.api.factory.detail}/${id}`)
  },

  /**
   * 新增工厂
   */
  createFactory(data) {
    return request.post(config.api.factory.create, data)
  },

  /**
   * 更新工厂信息
   */
  updateFactory(id, data) {
    return request.put(`${config.api.factory.update}/${id}`, data)
  },

  /**
   * 删除工厂
   */
  deleteFactory(id) {
    return request.delete(`${config.api.factory.delete}/${id}`)
  },

  /**
   * 获取所有启用的工厂列表
   */
  getActiveFactories() {
    return request.get(config.api.factory.active)
  }
}
