/**
 * API配置文件
 */

// 开发环境配置
const development = {
  baseURL: 'http://localhost:8080/api',
  timeout: 10000
}

// 生产环境配置
const production = {
  baseURL: 'http://************:8084/api',
  timeout: 10000
}

// 根据环境选择配置
const config = process.env.NODE_ENV === 'production' ? production : development

export default {
  ...config,
  
  // API接口地址
  api: {
    // 工厂管理
    factory: {
      list: '/factory/page',
      listAdmin: '/factory/page/admin',
      detail: '/factory',
      create: '/factory',
      update: '/factory',
      delete: '/factory',
      active: '/factory/active'
    },
    
    // 图片检索
    image: {
      list: '/image/page',
      detail: '/image',
      factories: '/image/factories',
      orders: '/image/orders',
      countByFactory: '/image/count/factory',
      countByOrder: '/image/count/order'
    },

    // 图片访问优化
    imageAccess: {
      url: '/image-access/url',
      urlSet: '/image-access/url-set',
      preload: '/image-access/preload',
      clearCache: '/image-access/cache'
    }
  }
}
