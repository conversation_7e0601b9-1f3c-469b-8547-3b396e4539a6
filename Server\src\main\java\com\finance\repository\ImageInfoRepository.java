package com.finance.repository;

import com.finance.entity.ImageInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图片信息数据访问层
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface ImageInfoRepository extends JpaRepository<ImageInfo, Integer> {

    /**
     * 根据工厂名称查询图片
     */
    Page<ImageInfo> findByFactoryName(String factoryName, Pageable pageable);

    /**
     * 根据订单号模糊查询图片
     */
    Page<ImageInfo> findByOrderNumberContaining(String orderNumber, Pageable pageable);

    /**
     * 根据工厂名称和订单号查询图片
     */
    Page<ImageInfo> findByFactoryNameAndOrderNumberContaining(String factoryName, String orderNumber, Pageable pageable);

    /**
     * 根据条件组合查询图片
     */
    @Query("SELECT i FROM ImageInfo i WHERE " +
           "(:factoryName IS NULL OR i.factoryName = :factoryName) AND " +
           "(:orderNumber IS NULL OR i.orderNumber LIKE %:orderNumber%) AND " +
           "(:imageName IS NULL OR i.imageName LIKE %:imageName%) AND " +
           "(:startDate IS NULL OR i.uploadDate >= :startDate) AND " +
           "(:endDate IS NULL OR i.uploadDate <= :endDate)")
    Page<ImageInfo> findByConditions(@Param("factoryName") String factoryName,
                                     @Param("orderNumber") String orderNumber,
                                     @Param("imageName") String imageName,
                                     @Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate,
                                     Pageable pageable);

    /**
     * 根据工厂名称统计图片数量
     */
    long countByFactoryName(String factoryName);

    /**
     * 根据订单号统计图片数量
     */
    long countByOrderNumber(String orderNumber);

    /**
     * 获取所有工厂名称列表
     */
    @Query("SELECT DISTINCT i.factoryName FROM ImageInfo i ORDER BY i.factoryName")
    List<String> findDistinctFactoryNames();

    /**
     * 根据工厂名称获取订单号列表
     */
    @Query("SELECT DISTINCT i.orderNumber FROM ImageInfo i WHERE i.factoryName = :factoryName ORDER BY i.orderNumber")
    List<String> findDistinctOrderNumbersByFactoryName(@Param("factoryName") String factoryName);

    /**
     * 根据时间范围查询图片
     */
    Page<ImageInfo> findByUploadDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
}
