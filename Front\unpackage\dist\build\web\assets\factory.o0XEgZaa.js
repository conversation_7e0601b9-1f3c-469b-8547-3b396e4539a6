import{r as t,c as a}from"./request.j3WTH_xy.js";const e={getFactoryPage:e=>t.get(a.api.factory.list,e),getFactoryPageAdmin:e=>t.get(a.api.factory.listAdmin,e),getFactoryById:e=>t.get(`${a.api.factory.detail}/${e}`),createFactory:e=>t.post(a.api.factory.create,e),updateFactory:(e,r)=>t.put(`${a.api.factory.update}/${e}`,r),deleteFactory:e=>t.delete(`${a.api.factory.delete}/${e}`),getActiveFactories:()=>t.get(a.api.factory.active)};export{e as f};
