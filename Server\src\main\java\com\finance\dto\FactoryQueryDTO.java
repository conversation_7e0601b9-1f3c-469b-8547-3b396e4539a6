package com.finance.dto;

import lombok.Data;

/**
 * 工厂查询条件DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class FactoryQueryDTO {

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 工厂名称（模糊查询）
     */
    private String factoryName;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 页码（从1开始）
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 20;
}
