/**
 * 图片预加载工具
 */

class ImagePreloader {
  constructor() {
    this.cache = new Map()
    this.loading = new Set()
    this.maxCacheSize = 50 // 最大缓存数量
  }

  /**
   * 预加载单张图片
   * @param {string} url 图片URL
   * @param {Object} options 选项
   * @returns {Promise} 加载Promise
   */
  preload(url, options = {}) {
    if (!url) return Promise.reject(new Error('URL is required'))
    
    // 如果已经缓存，直接返回
    if (this.cache.has(url)) {
      return Promise.resolve(this.cache.get(url))
    }
    
    // 如果正在加载，返回加载Promise
    if (this.loading.has(url)) {
      return this.loading.get(url)
    }
    
    const promise = this.loadImage(url, options)
    this.loading.set(url, promise)
    
    promise
      .then(result => {
        this.addToCache(url, result)
        this.loading.delete(url)
      })
      .catch(() => {
        this.loading.delete(url)
      })
    
    return promise
  }

  /**
   * 批量预加载图片
   * @param {Array} urls 图片URL数组
   * @param {Object} options 选项
   * @returns {Promise} 加载Promise
   */
  preloadBatch(urls, options = {}) {
    const { concurrent = 3 } = options
    
    return new Promise((resolve, reject) => {
      const results = []
      const errors = []
      let completed = 0
      
      const loadNext = (index) => {
        if (index >= urls.length) {
          if (completed === urls.length) {
            resolve({ results, errors })
          }
          return
        }
        
        this.preload(urls[index], options)
          .then(result => {
            results[index] = result
          })
          .catch(error => {
            errors[index] = error
          })
          .finally(() => {
            completed++
            loadNext(index + concurrent)
            
            if (completed === urls.length) {
              resolve({ results, errors })
            }
          })
      }
      
      // 启动并发加载
      for (let i = 0; i < Math.min(concurrent, urls.length); i++) {
        loadNext(i)
      }
    })
  }

  /**
   * 预加载图片集合（多尺寸）
   * @param {Array} imageSets 图片集合数组
   * @param {string} priority 优先级 ('thumbnail', 'medium', 'large')
   * @returns {Promise} 加载Promise
   */
  preloadImageSets(imageSets, priority = 'medium') {
    const urls = imageSets
      .map(imageSet => {
        if (typeof imageSet === 'string') return imageSet
        if (imageSet && typeof imageSet === 'object') {
          return imageSet[priority] || imageSet.medium || imageSet.thumbnail || imageSet.original
        }
        return null
      })
      .filter(Boolean)
    
    return this.preloadBatch(urls)
  }

  /**
   * 加载单张图片
   * @param {string} url 图片URL
   * @param {Object} options 选项
   * @returns {Promise} 加载Promise
   */
  loadImage(url, options = {}) {
    const { timeout = 10000 } = options
    
    return new Promise((resolve, reject) => {
      // #ifdef H5
      const img = new Image()
      
      const timer = setTimeout(() => {
        reject(new Error(`Image load timeout: ${url}`))
      }, timeout)
      
      img.onload = () => {
        clearTimeout(timer)
        resolve({
          url,
          width: img.naturalWidth,
          height: img.naturalHeight,
          loaded: true
        })
      }
      
      img.onerror = () => {
        clearTimeout(timer)
        reject(new Error(`Image load failed: ${url}`))
      }
      
      img.src = url
      // #endif
      
      // #ifndef H5
      // 小程序环境使用 uni.getImageInfo
      uni.getImageInfo({
        src: url,
        success: (res) => {
          resolve({
            url,
            width: res.width,
            height: res.height,
            loaded: true
          })
        },
        fail: (error) => {
          reject(new Error(`Image load failed: ${url}, ${error.errMsg}`))
        }
      })
      // #endif
    })
  }

  /**
   * 添加到缓存
   * @param {string} url 图片URL
   * @param {Object} data 图片数据
   */
  addToCache(url, data) {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(url, {
      ...data,
      cachedAt: Date.now()
    })
  }

  /**
   * 获取缓存的图片信息
   * @param {string} url 图片URL
   * @returns {Object|null} 缓存的图片信息
   */
  getCached(url) {
    return this.cache.get(url) || null
  }

  /**
   * 检查图片是否已缓存
   * @param {string} url 图片URL
   * @returns {boolean} 是否已缓存
   */
  isCached(url) {
    return this.cache.has(url)
  }

  /**
   * 清除缓存
   * @param {string} url 可选，指定URL清除单个缓存
   */
  clearCache(url) {
    if (url) {
      this.cache.delete(url)
    } else {
      this.cache.clear()
    }
  }

  /**
   * 清除过期缓存
   * @param {number} maxAge 最大缓存时间（毫秒）
   */
  clearExpiredCache(maxAge = 30 * 60 * 1000) { // 默认30分钟
    const now = Date.now()
    
    for (const [url, data] of this.cache.entries()) {
      if (now - data.cachedAt > maxAge) {
        this.cache.delete(url)
      }
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      loading: this.loading.size,
      urls: Array.from(this.cache.keys())
    }
  }
}

// 创建全局实例
const imagePreloader = new ImagePreloader()

// 定期清理过期缓存
setInterval(() => {
  imagePreloader.clearExpiredCache()
}, 5 * 60 * 1000) // 每5分钟清理一次

export default imagePreloader
