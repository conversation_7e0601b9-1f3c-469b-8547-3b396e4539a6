const e=new class{constructor(){this.timers=new Map,this.observers=new Map,this.metrics={renderTime:[],apiTime:[],memoryUsage:[]}}debounce(e,t=300,r=!1){let i;return function(...s){const n=r&&!i;clearTimeout(i),i=setTimeout((()=>{i=null,r||e.apply(this,s)}),t),n&&e.apply(this,s)}}throttle(e,t=100){let r;return function(...i){r||(e.apply(this,i),r=!0,setTimeout((()=>r=!1),t))}}defer(e,t=0){return setTimeout(e,t)}nextFrame(e){return"undefined"!=typeof requestAnimationFrame?requestAnimationFrame(e):setTimeout(e,16)}batchUpdate(e){this.nextFrame((()=>{e()}))}calculateVirtualScroll(e){const{totalItems:t,itemHeight:r,containerHeight:i,scrollTop:s,overscan:n=5}=e,a=Math.ceil(i/r),o=Math.max(0,Math.floor(s/r)-n);return{startIndex:o,endIndex:Math.min(t,o+a+2*n),visibleCount:a,offsetY:o*r}}observeImage(e,t,r={}){const i={rootMargin:"50px",threshold:.1,...r};if("undefined"!=typeof IntersectionObserver){const r=new IntersectionObserver((e=>{e.forEach((e=>{e.isIntersecting&&(t(e.target),r.unobserve(e.target))}))}),i);return r.observe(e),r}return t(e),null}monitorMemory(){if(performance.memory){const e={used:Math.round(performance.memory.usedJSHeapSize/1048576),total:Math.round(performance.memory.totalJSHeapSize/1048576),limit:Math.round(performance.memory.jsHeapSizeLimit/1048576)};return this.metrics.memoryUsage.push({...e,timestamp:Date.now()}),e}return null}timeStart(e){this.timers.set(e,Date.now())}timeEnd(e){const t=this.timers.get(e);if(t){const r=Date.now()-t;return this.timers.delete(e),e.includes("render")?this.metrics.renderTime.push(r):e.includes("api")&&this.metrics.apiTime.push(r),r}return 0}getPerformanceStats(){const e=e=>{if(0===e.length)return{avg:0,min:0,max:0,count:0};const t=e.reduce(((e,t)=>e+t),0);return{avg:Math.round(t/e.length),min:Math.min(...e),max:Math.max(...e),count:e.length}};return{renderTime:e(this.metrics.renderTime),apiTime:e(this.metrics.apiTime),memoryUsage:this.metrics.memoryUsage.slice(-10),currentMemory:this.monitorMemory()}}clearMetrics(){this.metrics.renderTime=[],this.metrics.apiTime=[],this.metrics.memoryUsage=[],this.timers.clear()}wrapRender(e,t){return(...r)=>{this.timeStart(`render-${t}`);const i=e.apply(this,r);return this.nextFrame((()=>{this.timeEnd(`render-${t}`)})),i}}processLargeList(e,t=100,r){let i=0;const s=()=>{const n=e.slice(i,i+t);n.length>0&&(r(n,i),i+=t,this.nextFrame(s))};s()}preloadResources(e,t="image"){const r=e.map((e=>new Promise(((r,i)=>{let s;switch(t){case"image":default:s=new Image;break;case"script":s=document.createElement("script");break;case"style":s=document.createElement("link"),s.rel="stylesheet"}s.onload=()=>r(e),s.onerror=()=>i(new Error(`Failed to load ${e}`)),s.src=e,"style"===t&&(s.href=e)}))));return Promise.allSettled(r)}};export{e as p};
