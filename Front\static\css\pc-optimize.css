/* PC端界面优化样式 */

/* 全局优化 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 响应式容器 */
.responsive-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

@media (min-width: 768px) {
  .responsive-container {
    padding: 0 40px;
  }
}

@media (min-width: 1200px) {
  .responsive-container {
    padding: 0 60px;
  }
}

/* 卡片样式优化 */
.enhanced-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.enhanced-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

@media (min-width: 768px) {
  .enhanced-card {
    border-radius: 20px;
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
  }
  
  .enhanced-card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

/* 按钮样式优化 */
.enhanced-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  outline: none;
}

.enhanced-btn:hover {
  transform: translateY(-2px);
}

.enhanced-btn:active {
  transform: translateY(0);
}

.enhanced-btn.primary {
  background: linear-gradient(135deg, #007AFF, #0056CC);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

.enhanced-btn.primary:hover {
  box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
}

.enhanced-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.enhanced-btn.secondary:hover {
  background: #e9ecef;
  color: #333;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .enhanced-btn {
    padding: 15px 30px;
    border-radius: 14px;
    font-size: 18px;
  }
}

/* 输入框样式优化 */
.enhanced-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  font-size: 16px;
  color: #333;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
}

.enhanced-input:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.enhanced-input::placeholder {
  color: #bbb;
}

@media (min-width: 768px) {
  .enhanced-input {
    padding: 15px 20px;
    border-radius: 14px;
    font-size: 18px;
  }
}

/* 网格布局优化 */
.responsive-grid {
  display: grid;
  gap: 20px;
}

.responsive-grid.cols-1 {
  grid-template-columns: 1fr;
}

.responsive-grid.cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.responsive-grid.cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.responsive-grid.cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (min-width: 768px) {
  .responsive-grid {
    gap: 25px;
  }
  
  .responsive-grid.auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (min-width: 1200px) {
  .responsive-grid {
    gap: 30px;
  }
  
  .responsive-grid.auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

/* 渐变背景 */
.gradient-bg-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 文字样式 */
.text-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 阴影效果 */
.shadow-sm {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shadow-md {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.shadow-xl {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 毛玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具类 */
.cursor-pointer {
  cursor: pointer;
}

.select-none {
  user-select: none;
}

.overflow-hidden {
  overflow: hidden;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式显示/隐藏 */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}
