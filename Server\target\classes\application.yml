server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: finance-web-server
  
  # 数据库配置
  datasource:
    url: *****************************************************************************************************************************************************
    username: mls01
    password: 12345@Mls
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: FinanceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none  # 不自动创建表，使用现有表结构
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    open-in-view: false

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.finance: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/finance-web.log

# 自定义配置
finance:
  # 图片存储路径配置
  image:
    storage-path: UploadImg/
    # 图片访问优化配置
    cdn:
      enabled: false
      domain: ""
    webp:
      enabled: true
    cache:
      enabled: true
      max-age: 86400  # 24小时

  # 服务器配置
  server:
    host: localhost
    port: 8080

  # 分页配置
  page:
    default-size: 20
    max-size: 100
