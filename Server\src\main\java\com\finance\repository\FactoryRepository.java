package com.finance.repository;

import com.finance.entity.Factory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 工厂数据访问层
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface FactoryRepository extends JpaRepository<Factory, Integer> {

    /**
     * 根据用户名查找工厂
     */
    Optional<Factory> findByUsername(String username);

    /**
     * 检查用户名是否存在（排除指定ID）
     */
    boolean existsByUsernameAndIdNot(String username, Integer id);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 根据条件分页查询工厂
     */
    @Query("SELECT f FROM Factory f WHERE " +
           "(:username IS NULL OR f.username LIKE %:username%) AND " +
           "(:factoryName IS NULL OR f.factoryName LIKE %:factoryName%) AND " +
           "(:status IS NULL OR f.status = :status)")
    Page<Factory> findByConditions(@Param("username") String username,
                                   @Param("factoryName") String factoryName,
                                   @Param("status") Integer status,
                                   Pageable pageable);

    /**
     * 根据状态查询工厂列表
     */
    Page<Factory> findByStatus(Integer status, Pageable pageable);
}
