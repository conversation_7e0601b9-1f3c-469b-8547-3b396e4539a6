/**
 * 图片访问优化API
 */
import request from '@/utils/request.js'
import config from '@/config/api.js'

export default {
  /**
   * 获取优化的图片URL
   */
  getOptimizedImageUrl(imagePath, size = 'medium') {
    return request.get(config.api.imageAccess.url, { imagePath, size })
  },

  /**
   * 获取图片的多种尺寸URL
   */
  getImageUrlSet(imagePath) {
    return request.get(config.api.imageAccess.urlSet, { imagePath })
  },

  /**
   * 批量预加载图片URL
   */
  preloadImageUrls(imagePaths, size = 'medium') {
    return request.post(config.api.imageAccess.preload + `?size=${size}`, imagePaths)
  },

  /**
   * 清除图片URL缓存
   */
  clearImageUrlCache(imagePath) {
    const params = imagePath ? { imagePath } : {}
    return request.delete(config.api.imageAccess.clearCache, params)
  }
}
