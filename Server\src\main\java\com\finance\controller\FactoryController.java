package com.finance.controller;

import com.finance.common.PageResult;
import com.finance.common.Result;
import com.finance.dto.FactoryDTO;
import com.finance.dto.FactoryQueryDTO;
import com.finance.service.FactoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工厂管理控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/factory")
public class FactoryController {

    @Autowired
    private FactoryService factoryService;

    /**
     * 分页查询工厂列表
     */
    @GetMapping("/page")
    public Result<PageResult<FactoryDTO>> getFactoryPage(FactoryQueryDTO queryDTO) {
        log.info("分页查询工厂列表，查询条件: {}", queryDTO);
        PageResult<FactoryDTO> pageResult = factoryService.getFactoryPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 分页查询工厂列表（包含密码信息，用于管理界面）
     */
    @GetMapping("/page/admin")
    public Result<PageResult<FactoryDTO>> getFactoryPageWithPassword(FactoryQueryDTO queryDTO) {
        log.info("分页查询工厂列表（管理界面），查询条件: {}", queryDTO);
        PageResult<FactoryDTO> pageResult = factoryService.getFactoryPageWithPassword(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID获取工厂信息
     */
    @GetMapping("/{id}")
    public Result<FactoryDTO> getFactoryById(@PathVariable Integer id) {
        log.info("获取工厂信息，ID: {}", id);
        FactoryDTO factory = factoryService.getFactoryById(id);
        return Result.success(factory);
    }

    /**
     * 新增工厂
     */
    @PostMapping
    public Result<FactoryDTO> createFactory(@Valid @RequestBody FactoryDTO factoryDTO) {
        log.info("新增工厂，数据: {}", factoryDTO);
        FactoryDTO createdFactory = factoryService.createFactory(factoryDTO);
        return Result.success("工厂创建成功", createdFactory);
    }

    /**
     * 更新工厂信息
     */
    @PutMapping("/{id}")
    public Result<FactoryDTO> updateFactory(@PathVariable Integer id, 
                                           @Valid @RequestBody FactoryDTO factoryDTO) {
        log.info("更新工厂信息，ID: {}, 数据: {}", id, factoryDTO);
        FactoryDTO updatedFactory = factoryService.updateFactory(id, factoryDTO);
        return Result.success("工厂更新成功", updatedFactory);
    }

    /**
     * 删除工厂
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteFactory(@PathVariable Integer id) {
        log.info("删除工厂，ID: {}", id);
        factoryService.deleteFactory(id);
        return Result.success("工厂删除成功");
    }

    /**
     * 获取所有启用的工厂列表
     */
    @GetMapping("/active")
    public Result<List<FactoryDTO>> getActiveFactories() {
        log.info("获取所有启用的工厂列表");
        List<FactoryDTO> factories = factoryService.getActiveFactories();
        return Result.success(factories);
    }
}
